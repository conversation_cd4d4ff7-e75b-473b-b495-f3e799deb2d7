import React, { useState } from 'react';
import {
  Image,
  ImageProps,
  View,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, SPACING } from '../../constants';

interface CachedImageProps extends Omit<ImageProps, 'source'> {
  uri: string;
  placeholder?: string;
  showLoader?: boolean;
  fallbackIcon?: keyof typeof Ionicons.glyphMap;
}

const CachedImage: React.FC<CachedImageProps> = ({
  uri,
  placeholder,
  showLoader = true,
  fallbackIcon = 'image-outline',
  style,
  ...props
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  const handleLoadStart = () => {
    setLoading(true);
    setError(false);
  };

  const handleLoadEnd = () => {
    setLoading(false);
  };

  const handleError = () => {
    setLoading(false);
    setError(true);
  };

  const imageSource = error && placeholder 
    ? { uri: placeholder }
    : { uri };

  return (
    <View style={[styles.container, style]}>
      <Image
        {...props}
        source={imageSource}
        style={[styles.image, style]}
        onLoadStart={handleLoadStart}
        onLoadEnd={handleLoadEnd}
        onError={handleError}
        resizeMode={props.resizeMode || 'cover'}
      />
      
      {loading && showLoader && (
        <View style={styles.overlay}>
          <ActivityIndicator size="small" color={COLORS.primary} />
        </View>
      )}
      
      {error && !placeholder && (
        <View style={styles.overlay}>
          <Ionicons 
            name={fallbackIcon} 
            size={24} 
            color={COLORS.gray} 
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: COLORS.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default CachedImage;
