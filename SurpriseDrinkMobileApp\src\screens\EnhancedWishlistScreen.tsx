import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Animated,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import type { NavigationProp } from '@react-navigation/native';

import { WooCommerceProduct, RootStackParamList } from '../types';
import { COLORS, FONTS, SPACING, BORDER_RADIUS, SHADOWS } from '../constants';
import { useApp } from '../context/AppContext';
import { useAuth } from '../context/AuthContext';
import ProductCard from '../components/product/ProductCard';
import AnimatedButton from '../components/common/AnimatedButton';
import PremiumCard from '../components/common/PremiumCard';

const { width } = Dimensions.get('window');

const EnhancedWishlistScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { state, removeFromWishlist, clearWishlist, addToCart } = useApp();
  const { state: authState } = useAuth();
  const { wishlist } = state;
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [removingItems, setRemovingItems] = useState<Set<number>>(new Set());

  const handleProductPress = (productId: number) => {
    navigation.navigate('ProductDetail', { productId });
  };

  const handleRemoveFromWishlist = (productId: number) => {
    Alert.alert(
      'Remove from Wishlist',
      'Are you sure you want to remove this item from your wishlist?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            setRemovingItems(prev => new Set(prev).add(productId));
            setTimeout(() => {
              removeFromWishlist(productId);
              setRemovingItems(prev => {
                const newSet = new Set(prev);
                newSet.delete(productId);
                return newSet;
              });
            }, 300);
          },
        },
      ]
    );
  };

  const handleClearWishlist = () => {
    Alert.alert(
      'Clear Wishlist',
      'Are you sure you want to remove all items from your wishlist?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: clearWishlist,
        },
      ]
    );
  };

  const handleAddToCart = (product: WooCommerceProduct) => {
    addToCart(product, 1);
    Alert.alert('Added to Cart', `${product.name} has been added to your cart.`);
  };

  const handleAddAllToCart = () => {
    Alert.alert(
      'Add All to Cart',
      `Add all ${wishlist.length} items to your cart?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Add All',
          onPress: () => {
            wishlist.forEach(product => addToCart(product, 1));
            Alert.alert('Success', `${wishlist.length} items added to cart!`);
          },
        },
      ]
    );
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.headerLeft}>
        <Text style={styles.headerTitle}>
          Wishlist {wishlist.length > 0 && `(${wishlist.length})`}
        </Text>
        {wishlist.length > 0 && (
          <Text style={styles.headerSubtitle}>
            Your saved items
          </Text>
        )}
      </View>
      
      {wishlist.length > 0 && (
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={[styles.viewModeButton, viewMode === 'grid' && styles.viewModeButtonActive]}
            onPress={() => setViewMode('grid')}
          >
            <Ionicons 
              name="grid-outline" 
              size={20} 
              color={viewMode === 'grid' ? COLORS.white : COLORS.gray} 
            />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.viewModeButton, viewMode === 'list' && styles.viewModeButtonActive]}
            onPress={() => setViewMode('list')}
          >
            <Ionicons 
              name="list-outline" 
              size={20} 
              color={viewMode === 'list' ? COLORS.white : COLORS.gray} 
            />
          </TouchableOpacity>
          
          <TouchableOpacity onPress={handleClearWishlist} style={styles.clearButton}>
            <Ionicons name="trash-outline" size={20} color={COLORS.error} />
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  const renderWishlistItem = ({ item, index }: { item: WooCommerceProduct; index: number }) => {
    const isRemoving = removingItems.has(item.id);
    
    return (
      <Animated.View
        style={[
          styles.productCardContainer,
          viewMode === 'grid' && styles.gridItem,
          viewMode === 'list' && styles.listItem,
        ]}
      >
        <ProductCard
          product={item}
          onPress={() => handleProductPress(item.id)}
          showAddToCart={true}
          style={viewMode === 'list' ? styles.listProductCard : undefined}
        />
        
        <View style={styles.itemActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleAddToCart(item)}
          >
            <Ionicons name="bag-add-outline" size={20} color={COLORS.primary} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, styles.removeButton]}
            onPress={() => handleRemoveFromWishlist(item.id)}
          >
            <Ionicons name="heart" size={20} color={COLORS.error} />
          </TouchableOpacity>
        </View>
      </Animated.View>
    );
  };

  const renderEmptyWishlist = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="heart-outline" size={80} color={COLORS.gray} />
      <Text style={styles.emptyTitle}>Your wishlist is empty</Text>
      <Text style={styles.emptySubtitle}>
        Save items you love to your wishlist
      </Text>
      <AnimatedButton
        title="Start Shopping"
        onPress={() => navigation.navigate('Home')}
        variant="primary"
        size="large"
        style={styles.shopButton}
        icon={<Ionicons name="bag-outline" size={20} color={COLORS.white} />}
      />
    </View>
  );

  const renderFooter = () => {
    if (wishlist.length === 0) return null;
    
    return (
      <PremiumCard style={styles.footerCard} padding="large" shadow="medium">
        <View style={styles.footerContent}>
          <View style={styles.footerStats}>
            <Text style={styles.footerTitle}>
              {wishlist.length} {wishlist.length === 1 ? 'Item' : 'Items'} in Wishlist
            </Text>
            <Text style={styles.footerSubtitle}>
              Ready to add to cart
            </Text>
          </View>
          
          <AnimatedButton
            title="Add All to Cart"
            onPress={handleAddAllToCart}
            variant="primary"
            size="medium"
            icon={<Ionicons name="bag-add-outline" size={20} color={COLORS.white} />}
          />
        </View>
      </PremiumCard>
    );
  };

  if (!authState.isAuthenticated) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.authRequired}>
          <Ionicons name="heart-outline" size={80} color={COLORS.gray} />
          <Text style={styles.authTitle}>Login Required</Text>
          <Text style={styles.authSubtitle}>
            Please login to view your wishlist
          </Text>
          <AnimatedButton
            title="Login"
            onPress={() => navigation.navigate('Login')}
            variant="primary"
            size="large"
            style={styles.loginButton}
            icon={<Ionicons name="log-in-outline" size={20} color={COLORS.white} />}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {renderHeader()}
      
      {wishlist.length === 0 ? (
        renderEmptyWishlist()
      ) : (
        <FlatList
          data={wishlist}
          renderItem={renderWishlistItem}
          keyExtractor={(item) => item.id.toString()}
          numColumns={viewMode === 'grid' ? 2 : 1}
          key={viewMode} // Force re-render when view mode changes
          contentContainerStyle={styles.listContainer}
          columnWrapperStyle={viewMode === 'grid' ? styles.row : undefined}
          showsVerticalScrollIndicator={false}
          ListFooterComponent={renderFooter}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    backgroundColor: COLORS.white,
    ...SHADOWS.light,
  },
  headerLeft: {
    flex: 1,
  },
  headerTitle: {
    fontSize: FONTS.xl,
    fontWeight: '600',
    color: COLORS.textPrimary,
  },
  headerSubtitle: {
    fontSize: FONTS.sm,
    color: COLORS.textSecondary,
    marginTop: SPACING.xs,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
  },
  viewModeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
  },
  viewModeButtonActive: {
    backgroundColor: COLORS.primary,
  },
  clearButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.error + '15',
    alignItems: 'center',
    justifyContent: 'center',
  },
  listContainer: {
    padding: SPACING.lg,
  },
  row: {
    justifyContent: 'space-between',
  },
  productCardContainer: {
    position: 'relative',
    marginBottom: SPACING.md,
  },
  gridItem: {
    width: (width - SPACING.lg * 3) / 2,
  },
  listItem: {
    width: '100%',
  },
  listProductCard: {
    // Additional styles for list view if needed
  },
  itemActions: {
    position: 'absolute',
    top: SPACING.sm,
    right: SPACING.sm,
    flexDirection: 'row',
    gap: SPACING.xs,
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: COLORS.white,
    alignItems: 'center',
    justifyContent: 'center',
    ...SHADOWS.light,
  },
  removeButton: {
    backgroundColor: COLORS.error + '15',
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: SPACING.xl,
  },
  emptyTitle: {
    fontSize: FONTS.xl,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginTop: SPACING.lg,
    marginBottom: SPACING.sm,
  },
  emptySubtitle: {
    fontSize: FONTS.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xl,
  },
  shopButton: {
    marginTop: SPACING.lg,
  },
  footerCard: {
    margin: SPACING.lg,
    marginTop: 0,
  },
  footerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  footerStats: {
    flex: 1,
  },
  footerTitle: {
    fontSize: FONTS.lg,
    fontWeight: '600',
    color: COLORS.textPrimary,
  },
  footerSubtitle: {
    fontSize: FONTS.sm,
    color: COLORS.textSecondary,
    marginTop: SPACING.xs,
  },
  authRequired: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: SPACING.xl,
  },
  authTitle: {
    fontSize: FONTS.xl,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginTop: SPACING.lg,
    marginBottom: SPACING.sm,
  },
  authSubtitle: {
    fontSize: FONTS.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xl,
  },
  loginButton: {
    marginTop: SPACING.lg,
  },
});

export default EnhancedWishlistScreen;
