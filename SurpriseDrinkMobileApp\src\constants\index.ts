import { Dimensions } from 'react-native';

// Screen dimensions
export const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Colors - Premium lingerie theme
export const COLORS = {
  // Primary colors
  primary: '#E91E63', // Pink
  primaryDark: '#C2185B',
  primaryLight: '#F8BBD9',
  
  // Secondary colors
  secondary: '#9C27B0', // Purple
  secondaryDark: '#7B1FA2',
  secondaryLight: '#E1BEE7',
  
  // Accent colors
  accent: '#FF4081',
  accentLight: '#FF80AB',
  
  // Neutral colors
  white: '#FFFFFF',
  black: '#000000',
  gray: '#9E9E9E',
  lightGray: '#F5F5F5',
  darkGray: '#424242',
  
  // Background colors
  background: '#FAFAFA',
  surface: '#FFFFFF',
  
  // Text colors
  textPrimary: '#212121',
  textSecondary: '#757575',
  textLight: '#FFFFFF',
  
  // Status colors
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',
  
  // Border colors
  border: '#E0E0E0',
  divider: '#BDBDBD',
  
  // Overlay
  overlay: 'rgba(0, 0, 0, 0.5)',
  overlayLight: 'rgba(0, 0, 0, 0.3)',
};

// Typography
export const FONTS = {
  // Font families
  regular: 'System',
  medium: 'System',
  bold: 'System',
  light: 'System',
  
  // Font sizes
  xs: 12,
  sm: 14,
  md: 16,
  lg: 18,
  xl: 20,
  xxl: 24,
  xxxl: 28,
  
  // Line heights
  lineHeight: {
    xs: 16,
    sm: 20,
    md: 24,
    lg: 28,
    xl: 32,
    xxl: 36,
    xxxl: 40,
  },
};

// Spacing
export const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  xxxl: 64,
};

// Border radius
export const BORDER_RADIUS = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  round: 50,
};

// Shadows
export const SHADOWS = {
  light: {
    shadowColor: COLORS.black,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,
    elevation: 1,
  },
  medium: {
    shadowColor: COLORS.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  heavy: {
    shadowColor: COLORS.black,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.30,
    shadowRadius: 4.65,
    elevation: 8,
  },
};

// Animation durations
export const ANIMATION_DURATION = {
  fast: 200,
  medium: 300,
  slow: 500,
};

// API Configuration
export const API_CONFIG = {
  BASE_URL: 'https://surprisedrink.s2-tastewp.com',
  WC_ENDPOINT: '/wp-json/wc/v3',
  CONSUMER_KEY: 'ck_3526e1fdad71a83efe18181c26ca32caa5d73263',
  CONSUMER_SECRET: 'cs_e6320b015cda94e4a91cd27a32cc25839db4dce7',
  TIMEOUT: 10000,
};

// App Configuration
export const APP_CONFIG = {
  APP_NAME: 'Surprise Drink',
  VERSION: '1.0.0',
  CURRENCY: 'USD',
  CURRENCY_SYMBOL: '$',
  ITEMS_PER_PAGE: 20,
  MAX_CART_QUANTITY: 99,
  WISHLIST_MAX_ITEMS: 100,
};

// Storage Keys
export const STORAGE_KEYS = {
  USER_TOKEN: '@user_token',
  USER_DATA: '@user_data',
  CART_DATA: '@cart_data',
  WISHLIST_DATA: '@wishlist_data',
  RECENT_SEARCHES: '@recent_searches',
  APP_SETTINGS: '@app_settings',
};

// Product categories mapping (based on your website)
export const PRODUCT_CATEGORIES = {
  FURNITURE: 'furniture',
  ACCESSORIES: 'accessories',
  LIGHTING: 'lighting',
  CLOCKS: 'clocks',
  TOYS: 'toys',
  COOKING: 'cooking',
};

// Order status mapping
export const ORDER_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  ON_HOLD: 'on-hold',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  REFUNDED: 'refunded',
  FAILED: 'failed',
};

// Payment methods
export const PAYMENT_METHODS = {
  CASH_ON_DELIVERY: 'cod',
  BANK_TRANSFER: 'bacs',
  PAYPAL: 'paypal',
  STRIPE: 'stripe',
};

// Image placeholders
export const PLACEHOLDER_IMAGES = {
  PRODUCT: 'https://via.placeholder.com/300x300/E91E63/FFFFFF?text=Product',
  CATEGORY: 'https://via.placeholder.com/200x200/9C27B0/FFFFFF?text=Category',
  USER: 'https://via.placeholder.com/100x100/757575/FFFFFF?text=User',
  BANNER: 'https://via.placeholder.com/400x200/E91E63/FFFFFF?text=Banner',
};

// Regular expressions
export const REGEX = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^[\+]?[1-9][\d]{0,15}$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
  POSTAL_CODE: /^[0-9]{5}(-[0-9]{4})?$/,
};

// Error messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your internet connection.',
  INVALID_EMAIL: 'Please enter a valid email address.',
  INVALID_PASSWORD: 'Password must be at least 8 characters with uppercase, lowercase, and number.',
  REQUIRED_FIELD: 'This field is required.',
  PRODUCT_NOT_FOUND: 'Product not found.',
  CART_EMPTY: 'Your cart is empty.',
  LOGIN_REQUIRED: 'Please login to continue.',
  GENERIC_ERROR: 'Something went wrong. Please try again.',
};

// Success messages
export const SUCCESS_MESSAGES = {
  PRODUCT_ADDED_TO_CART: 'Product added to cart successfully!',
  PRODUCT_ADDED_TO_WISHLIST: 'Product added to wishlist!',
  PRODUCT_REMOVED_FROM_WISHLIST: 'Product removed from wishlist!',
  ORDER_PLACED: 'Order placed successfully!',
  PROFILE_UPDATED: 'Profile updated successfully!',
  PASSWORD_CHANGED: 'Password changed successfully!',
};

// Feature flags
export const FEATURES = {
  WISHLIST_ENABLED: true,
  REVIEWS_ENABLED: true,
  SOCIAL_LOGIN_ENABLED: false,
  PUSH_NOTIFICATIONS_ENABLED: true,
  ANALYTICS_ENABLED: true,
  DARK_MODE_ENABLED: false,
};
