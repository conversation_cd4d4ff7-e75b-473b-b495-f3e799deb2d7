import { REGEX } from '../constants';

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: string) => boolean;
  message?: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export class Validator {
  static validateField(value: string, rules: ValidationRule[]): ValidationResult {
    const errors: string[] = [];

    for (const rule of rules) {
      if (rule.required && (!value || value.trim().length === 0)) {
        errors.push(rule.message || 'This field is required');
        continue;
      }

      if (value && rule.minLength && value.length < rule.minLength) {
        errors.push(rule.message || `Minimum length is ${rule.minLength} characters`);
      }

      if (value && rule.maxLength && value.length > rule.maxLength) {
        errors.push(rule.message || `Maximum length is ${rule.maxLength} characters`);
      }

      if (value && rule.pattern && !rule.pattern.test(value)) {
        errors.push(rule.message || 'Invalid format');
      }

      if (value && rule.custom && !rule.custom(value)) {
        errors.push(rule.message || 'Invalid value');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  static validateForm(data: Record<string, string>, rules: Record<string, ValidationRule[]>): {
    isValid: boolean;
    errors: Record<string, string[]>;
  } {
    const errors: Record<string, string[]> = {};
    let isValid = true;

    for (const [field, fieldRules] of Object.entries(rules)) {
      const value = data[field] || '';
      const result = this.validateField(value, fieldRules);
      
      if (!result.isValid) {
        errors[field] = result.errors;
        isValid = false;
      }
    }

    return { isValid, errors };
  }

  // Common validation rules
  static email(message?: string): ValidationRule {
    return {
      pattern: REGEX.EMAIL,
      message: message || 'Please enter a valid email address',
    };
  }

  static required(message?: string): ValidationRule {
    return {
      required: true,
      message: message || 'This field is required',
    };
  }

  static minLength(length: number, message?: string): ValidationRule {
    return {
      minLength: length,
      message: message || `Minimum length is ${length} characters`,
    };
  }

  static maxLength(length: number, message?: string): ValidationRule {
    return {
      maxLength: length,
      message: message || `Maximum length is ${length} characters`,
    };
  }

  static password(message?: string): ValidationRule {
    return {
      pattern: REGEX.PASSWORD,
      message: message || 'Password must be at least 8 characters with uppercase, lowercase, and number',
    };
  }

  static phone(message?: string): ValidationRule {
    return {
      pattern: REGEX.PHONE,
      message: message || 'Please enter a valid phone number',
    };
  }

  static postalCode(message?: string): ValidationRule {
    return {
      pattern: REGEX.POSTAL_CODE,
      message: message || 'Please enter a valid postal code',
    };
  }

  static confirmPassword(originalPassword: string, message?: string): ValidationRule {
    return {
      custom: (value: string) => value === originalPassword,
      message: message || 'Passwords do not match',
    };
  }

  static numeric(message?: string): ValidationRule {
    return {
      pattern: /^\d+$/,
      message: message || 'Please enter numbers only',
    };
  }

  static alphanumeric(message?: string): ValidationRule {
    return {
      pattern: /^[a-zA-Z0-9]+$/,
      message: message || 'Please enter letters and numbers only',
    };
  }

  static url(message?: string): ValidationRule {
    return {
      pattern: /^https?:\/\/.+/,
      message: message || 'Please enter a valid URL',
    };
  }

  static creditCard(message?: string): ValidationRule {
    return {
      pattern: /^\d{4}\s?\d{4}\s?\d{4}\s?\d{4}$/,
      message: message || 'Please enter a valid credit card number',
    };
  }

  static cvv(message?: string): ValidationRule {
    return {
      pattern: /^\d{3,4}$/,
      message: message || 'Please enter a valid CVV',
    };
  }

  static expiryDate(message?: string): ValidationRule {
    return {
      pattern: /^(0[1-9]|1[0-2])\/\d{2}$/,
      custom: (value: string) => {
        if (!/^(0[1-9]|1[0-2])\/\d{2}$/.test(value)) return false;
        
        const [month, year] = value.split('/');
        const expiry = new Date(2000 + parseInt(year), parseInt(month) - 1);
        const now = new Date();
        
        return expiry > now;
      },
      message: message || 'Please enter a valid expiry date (MM/YY)',
    };
  }
}

// Hook for form validation
import { useState, useCallback } from 'react';

export function useFormValidation(
  initialData: Record<string, string>,
  validationRules: Record<string, ValidationRule[]>
) {
  const [data, setData] = useState(initialData);
  const [errors, setErrors] = useState<Record<string, string[]>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  const validateField = useCallback((field: string, value: string) => {
    const rules = validationRules[field];
    if (!rules) return;

    const result = Validator.validateField(value, rules);
    setErrors(prev => ({
      ...prev,
      [field]: result.errors,
    }));
  }, [validationRules]);

  const setValue = useCallback((field: string, value: string) => {
    setData(prev => ({ ...prev, [field]: value }));
    
    if (touched[field]) {
      validateField(field, value);
    }
  }, [touched, validateField]);

  const setFieldTouched = useCallback((field: string) => {
    setTouched(prev => ({ ...prev, [field]: true }));
    validateField(field, data[field] || '');
  }, [data, validateField]);

  const validateAll = useCallback(() => {
    const result = Validator.validateForm(data, validationRules);
    setErrors(result.errors);
    
    // Mark all fields as touched
    const allTouched = Object.keys(validationRules).reduce((acc, field) => {
      acc[field] = true;
      return acc;
    }, {} as Record<string, boolean>);
    setTouched(allTouched);
    
    return result.isValid;
  }, [data, validationRules]);

  const reset = useCallback(() => {
    setData(initialData);
    setErrors({});
    setTouched({});
  }, [initialData]);

  const getFieldError = useCallback((field: string) => {
    return touched[field] && errors[field] ? errors[field][0] : undefined;
  }, [touched, errors]);

  const hasErrors = Object.values(errors).some(fieldErrors => fieldErrors.length > 0);
  const isValid = !hasErrors && Object.keys(touched).length > 0;

  return {
    data,
    errors,
    touched,
    setValue,
    setFieldTouched,
    validateAll,
    reset,
    getFieldError,
    hasErrors,
    isValid,
  };
}
