// Authentication test utilities and helpers

import authService from '../services/auth';
import { User } from '../types';

// Test user data
export const testUsers = {
  validUser: {
    email: '<EMAIL>',
    password: 'password',
    firstName: 'Demo',
    lastName: 'User',
  },
  invalidUser: {
    email: '<EMAIL>',
    password: 'wrongpassword',
  },
  newUser: {
    firstName: 'New',
    lastName: 'User',
    email: '<EMAIL>',
    password: 'newpassword123',
    phone: '+1234567890',
  },
};

// Test authentication flows
export const testAuthFlow = {
  // Test login flow
  async testLogin(): Promise<boolean> {
    try {
      console.log('Testing login flow...');
      
      // Test valid login
      const response = await authService.login({
        email: testUsers.validUser.email,
        password: testUsers.validUser.password,
      });
      
      if (!response.user || !response.token) {
        console.error('Login failed: No user or token returned');
        return false;
      }
      
      // Verify user is authenticated
      const currentUser = authService.getCurrentUser();
      if (!currentUser || currentUser.email !== testUsers.validUser.email) {
        console.error('Login failed: Current user not set correctly');
        return false;
      }
      
      console.log('Login test passed ✓');
      return true;
    } catch (error) {
      console.error('Login test failed:', error);
      return false;
    }
  },

  // Test registration flow
  async testRegistration(): Promise<boolean> {
    try {
      console.log('Testing registration flow...');
      
      // Logout first if logged in
      if (authService.isAuthenticated()) {
        await authService.logout();
      }
      
      const response = await authService.register(testUsers.newUser);
      
      if (!response.user || !response.token) {
        console.error('Registration failed: No user or token returned');
        return false;
      }
      
      // Verify user data
      const user = response.user;
      if (user.firstName !== testUsers.newUser.firstName || 
          user.lastName !== testUsers.newUser.lastName ||
          user.email !== testUsers.newUser.email) {
        console.error('Registration failed: User data mismatch');
        return false;
      }
      
      console.log('Registration test passed ✓');
      return true;
    } catch (error) {
      console.error('Registration test failed:', error);
      return false;
    }
  },

  // Test logout flow
  async testLogout(): Promise<boolean> {
    try {
      console.log('Testing logout flow...');
      
      // Ensure user is logged in first
      if (!authService.isAuthenticated()) {
        await authService.login({
          email: testUsers.validUser.email,
          password: testUsers.validUser.password,
        });
      }
      
      await authService.logout();
      
      // Verify user is logged out
      if (authService.isAuthenticated() || authService.getCurrentUser()) {
        console.error('Logout failed: User still authenticated');
        return false;
      }
      
      console.log('Logout test passed ✓');
      return true;
    } catch (error) {
      console.error('Logout test failed:', error);
      return false;
    }
  },

  // Test profile update
  async testProfileUpdate(): Promise<boolean> {
    try {
      console.log('Testing profile update flow...');
      
      // Ensure user is logged in
      if (!authService.isAuthenticated()) {
        await authService.login({
          email: testUsers.validUser.email,
          password: testUsers.validUser.password,
        });
      }
      
      const updates = {
        firstName: 'Updated',
        lastName: 'Name',
      };
      
      const updatedUser = await authService.updateProfile(updates);
      
      if (updatedUser.firstName !== updates.firstName || 
          updatedUser.lastName !== updates.lastName) {
        console.error('Profile update failed: Data not updated correctly');
        return false;
      }
      
      console.log('Profile update test passed ✓');
      return true;
    } catch (error) {
      console.error('Profile update test failed:', error);
      return false;
    }
  },

  // Test password change
  async testPasswordChange(): Promise<boolean> {
    try {
      console.log('Testing password change flow...');
      
      // Ensure user is logged in
      if (!authService.isAuthenticated()) {
        await authService.login({
          email: testUsers.validUser.email,
          password: testUsers.validUser.password,
        });
      }
      
      await authService.changePassword('password', 'newpassword123');
      
      console.log('Password change test passed ✓');
      return true;
    } catch (error) {
      console.error('Password change test failed:', error);
      return false;
    }
  },

  // Test password reset request
  async testPasswordReset(): Promise<boolean> {
    try {
      console.log('Testing password reset flow...');
      
      await authService.requestPasswordReset(testUsers.validUser.email);
      
      console.log('Password reset test passed ✓');
      return true;
    } catch (error) {
      console.error('Password reset test failed:', error);
      return false;
    }
  },

  // Test token refresh
  async testTokenRefresh(): Promise<boolean> {
    try {
      console.log('Testing token refresh flow...');
      
      // Ensure user is logged in
      if (!authService.isAuthenticated()) {
        await authService.login({
          email: testUsers.validUser.email,
          password: testUsers.validUser.password,
        });
      }
      
      const newToken = await authService.refreshToken();
      
      if (!newToken || typeof newToken !== 'string') {
        console.error('Token refresh failed: No token returned');
        return false;
      }
      
      console.log('Token refresh test passed ✓');
      return true;
    } catch (error) {
      console.error('Token refresh test failed:', error);
      return false;
    }
  },

  // Run all tests
  async runAllTests(): Promise<void> {
    console.log('🧪 Starting authentication tests...\n');
    
    const tests = [
      { name: 'Login', test: this.testLogin },
      { name: 'Registration', test: this.testRegistration },
      { name: 'Profile Update', test: this.testProfileUpdate },
      { name: 'Password Change', test: this.testPasswordChange },
      { name: 'Password Reset', test: this.testPasswordReset },
      { name: 'Token Refresh', test: this.testTokenRefresh },
      { name: 'Logout', test: this.testLogout },
    ];
    
    let passed = 0;
    let failed = 0;
    
    for (const { name, test } of tests) {
      try {
        const result = await test.call(this);
        if (result) {
          passed++;
        } else {
          failed++;
        }
      } catch (error) {
        console.error(`❌ ${name} test threw an error:`, error);
        failed++;
      }
      console.log(''); // Add spacing between tests
    }
    
    console.log(`\n📊 Test Results:`);
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
    
    if (failed === 0) {
      console.log('🎉 All authentication tests passed!');
    } else {
      console.log('⚠️  Some tests failed. Please check the implementation.');
    }
  },
};

// Utility functions for testing
export const authTestUtils = {
  // Clear all auth data
  async clearAuthData(): Promise<void> {
    try {
      await authService.logout();
    } catch (error) {
      console.error('Error clearing auth data:', error);
    }
  },

  // Create a test user session
  async createTestSession(): Promise<User | null> {
    try {
      const response = await authService.login({
        email: testUsers.validUser.email,
        password: testUsers.validUser.password,
      });
      return response.user;
    } catch (error) {
      console.error('Error creating test session:', error);
      return null;
    }
  },

  // Verify authentication state
  verifyAuthState(expectedState: boolean): boolean {
    const isAuthenticated = authService.isAuthenticated();
    const currentUser = authService.getCurrentUser();
    
    if (expectedState) {
      return isAuthenticated && currentUser !== null;
    } else {
      return !isAuthenticated && currentUser === null;
    }
  },

  // Mock network delay for testing
  async mockDelay(ms: number = 1000): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  },
};
