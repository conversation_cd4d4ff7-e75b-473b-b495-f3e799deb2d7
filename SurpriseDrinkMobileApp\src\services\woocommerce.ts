import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  WooCommerceProduct,
  WooCommerceCategory,
  WooCommerceCustomer,
  WooCommerceOrder,
  WooCommerceBillingAddress,
  WooCommerceShippingAddress,
} from '../types';
import { ErrorHandler, withRetry } from '../utils/errorHandler';
import { mockApiResponses, simulateApiDelay, log, logError } from '../utils/testHelpers';

class WooCommerceAPI {
  private api: AxiosInstance;
  private baseURL: string;
  private consumerKey: string;
  private consumerSecret: string;
  private useMockData: boolean = false;

  constructor() {
    this.baseURL = 'https://surprisedrink.s2-tastewp.com';
    this.consumerKey = 'ck_3526e1fdad71a83efe18181c26ca32caa5d73263';
    this.consumerSecret = 'cs_e6320b015cda94e4a91cd27a32cc25839db4dce7';

    this.api = axios.create({
      baseURL: `${this.baseURL}/wp-json/wc/v3`,
      auth: {
        username: this.consumerKey,
        password: this.consumerSecret,
      },
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        console.log(`Making API request to: ${config.url}`);
        return config;
      },
      (error) => {
        console.error('Request error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response) => {
        return response;
      },
      (error) => {
        console.error('Response error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  // Products
  async getProducts(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    category?: number;
    featured?: boolean;
    on_sale?: boolean;
    orderby?: 'date' | 'id' | 'include' | 'title' | 'slug' | 'price' | 'popularity' | 'rating';
    order?: 'asc' | 'desc';
  }): Promise<WooCommerceProduct[]> {
    try {
      const response: AxiosResponse<WooCommerceProduct[]> = await withRetry(
        () => this.api.get('/products', {
          params: {
            per_page: 20,
            ...params,
          },
        })
      );
      return response.data;
    } catch (error) {
      throw ErrorHandler.handleApiError(error);
    }
  }

  async getProduct(id: number): Promise<WooCommerceProduct> {
    try {
      const response: AxiosResponse<WooCommerceProduct> = await this.api.get(`/products/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching product:', error);
      throw error;
    }
  }

  async getFeaturedProducts(): Promise<WooCommerceProduct[]> {
    try {
      return await this.getProducts({ featured: true, per_page: 10 });
    } catch (error) {
      log('Using mock data for featured products');
      await simulateApiDelay(500);
      return mockApiResponses.featuredProducts;
    }
  }

  async getOnSaleProducts(): Promise<WooCommerceProduct[]> {
    try {
      return await this.getProducts({ on_sale: true, per_page: 10 });
    } catch (error) {
      log('Using mock data for on sale products');
      await simulateApiDelay(500);
      return mockApiResponses.onSaleProducts;
    }
  }

  async getLatestProducts(): Promise<WooCommerceProduct[]> {
    try {
      return await this.getProducts({ orderby: 'date', order: 'desc', per_page: 10 });
    } catch (error) {
      log('Using mock data for latest products');
      await simulateApiDelay(500);
      return mockApiResponses.latestProducts;
    }
  }

  async searchProducts(query: string, page: number = 1): Promise<WooCommerceProduct[]> {
    return this.getProducts({ search: query, page, per_page: 20 });
  }

  // Categories
  async getCategories(params?: {
    page?: number;
    per_page?: number;
    parent?: number;
    hide_empty?: boolean;
  }): Promise<WooCommerceCategory[]> {
    try {
      const response: AxiosResponse<WooCommerceCategory[]> = await this.api.get('/products/categories', {
        params: {
          per_page: 100,
          hide_empty: true,
          ...params,
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching categories:', error);
      throw error;
    }
  }

  async getCategory(id: number): Promise<WooCommerceCategory> {
    try {
      const response: AxiosResponse<WooCommerceCategory> = await this.api.get(`/products/categories/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching category:', error);
      throw error;
    }
  }

  async getProductsByCategory(categoryId: number, page: number = 1): Promise<WooCommerceProduct[]> {
    return this.getProducts({ category: categoryId, page, per_page: 20 });
  }

  // Customers
  async createCustomer(customerData: {
    email: string;
    first_name: string;
    last_name: string;
    username: string;
    password: string;
    billing?: Partial<WooCommerceBillingAddress>;
    shipping?: Partial<WooCommerceShippingAddress>;
  }): Promise<WooCommerceCustomer> {
    try {
      const response: AxiosResponse<WooCommerceCustomer> = await this.api.post('/customers', customerData);
      return response.data;
    } catch (error) {
      console.error('Error creating customer:', error);
      throw error;
    }
  }

  async getCustomer(id: number): Promise<WooCommerceCustomer> {
    try {
      const response: AxiosResponse<WooCommerceCustomer> = await this.api.get(`/customers/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching customer:', error);
      throw error;
    }
  }

  async updateCustomer(id: number, customerData: Partial<WooCommerceCustomer>): Promise<WooCommerceCustomer> {
    try {
      const response: AxiosResponse<WooCommerceCustomer> = await this.api.put(`/customers/${id}`, customerData);
      return response.data;
    } catch (error) {
      console.error('Error updating customer:', error);
      throw error;
    }
  }

  // Orders
  async createOrder(orderData: {
    payment_method: string;
    payment_method_title: string;
    set_paid: boolean;
    billing: WooCommerceBillingAddress;
    shipping: WooCommerceShippingAddress;
    line_items: Array<{
      product_id: number;
      quantity: number;
      variation_id?: number;
    }>;
    customer_id?: number;
  }): Promise<WooCommerceOrder> {
    try {
      const response: AxiosResponse<WooCommerceOrder> = await this.api.post('/orders', orderData);
      return response.data;
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  }

  async getOrders(customerId?: number): Promise<WooCommerceOrder[]> {
    try {
      const params: any = { per_page: 100 };
      if (customerId) {
        params.customer = customerId;
      }
      const response: AxiosResponse<WooCommerceOrder[]> = await this.api.get('/orders', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching orders:', error);
      throw error;
    }
  }

  async getOrder(id: number): Promise<WooCommerceOrder> {
    try {
      const response: AxiosResponse<WooCommerceOrder> = await this.api.get(`/orders/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching order:', error);
      throw error;
    }
  }

  // Authentication helper (for WordPress JWT)
  async authenticateUser(username: string, password: string): Promise<any> {
    try {
      // This would typically use JWT authentication plugin
      // For now, we'll simulate authentication
      const response = await axios.post(`${this.baseURL}/wp-json/jwt-auth/v1/token`, {
        username,
        password,
      });
      return response.data;
    } catch (error) {
      console.error('Authentication error:', error);
      throw error;
    }
  }

  // Utility methods
  formatPrice(price: string, currency: string = 'USD'): string {
    const numPrice = parseFloat(price);
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(numPrice);
  }

  isProductOnSale(product: WooCommerceProduct): boolean {
    return product.on_sale && parseFloat(product.sale_price) < parseFloat(product.regular_price);
  }

  getDiscountPercentage(product: WooCommerceProduct): number {
    if (!this.isProductOnSale(product)) return 0;
    const regular = parseFloat(product.regular_price);
    const sale = parseFloat(product.sale_price);
    return Math.round(((regular - sale) / regular) * 100);
  }
}

export default new WooCommerceAPI();
