import { useState, useEffect, useCallback } from 'react';

interface UseLazyLoadOptions<T> {
  loadFunction: (page: number) => Promise<T[]>;
  initialPage?: number;
  pageSize?: number;
  dependencies?: any[];
}

interface UseLazyLoadReturn<T> {
  data: T[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  loadMore: () => void;
  refresh: () => void;
  reset: () => void;
}

export function useLazyLoad<T>({
  loadFunction,
  initialPage = 1,
  pageSize = 20,
  dependencies = [],
}: UseLazyLoadOptions<T>): UseLazyLoadReturn<T> {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [hasMore, setHasMore] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const loadData = useCallback(
    async (page: number, isRefresh: boolean = false) => {
      if (loading && !isRefresh) return;

      try {
        setLoading(true);
        setError(null);

        const newData = await loadFunction(page);
        
        if (isRefresh || page === initialPage) {
          setData(newData);
        } else {
          setData(prevData => [...prevData, ...newData]);
        }

        // Check if there's more data
        setHasMore(newData.length === pageSize);
        
        if (!isRefresh) {
          setCurrentPage(page);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        console.error('Error loading data:', err);
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    },
    [loadFunction, pageSize, initialPage, loading]
  );

  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      loadData(currentPage + 1);
    }
  }, [loadData, loading, hasMore, currentPage]);

  const refresh = useCallback(() => {
    setRefreshing(true);
    setCurrentPage(initialPage);
    setHasMore(true);
    loadData(initialPage, true);
  }, [loadData, initialPage]);

  const reset = useCallback(() => {
    setData([]);
    setCurrentPage(initialPage);
    setHasMore(true);
    setError(null);
    setLoading(false);
    setRefreshing(false);
  }, [initialPage]);

  // Initial load and dependency changes
  useEffect(() => {
    reset();
    loadData(initialPage, true);
  }, dependencies);

  return {
    data,
    loading: loading && !refreshing,
    error,
    hasMore,
    loadMore,
    refresh,
    reset,
  };
}

// Hook for infinite scroll with FlatList
export function useInfiniteScroll<T>(options: UseLazyLoadOptions<T>) {
  const lazyLoad = useLazyLoad(options);

  const onEndReached = useCallback(() => {
    lazyLoad.loadMore();
  }, [lazyLoad.loadMore]);

  const onRefresh = useCallback(() => {
    lazyLoad.refresh();
  }, [lazyLoad.refresh]);

  return {
    ...lazyLoad,
    onEndReached,
    onRefresh,
    refreshing: lazyLoad.loading,
  };
}

// Hook for search with debouncing
export function useSearchWithDebounce<T>(
  searchFunction: (query: string) => Promise<T[]>,
  debounceMs: number = 300
) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!query.trim()) {
      setResults([]);
      return;
    }

    const timeoutId = setTimeout(async () => {
      try {
        setLoading(true);
        setError(null);
        const searchResults = await searchFunction(query);
        setResults(searchResults);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Search failed');
        setResults([]);
      } finally {
        setLoading(false);
      }
    }, debounceMs);

    return () => clearTimeout(timeoutId);
  }, [query, searchFunction, debounceMs]);

  return {
    query,
    setQuery,
    results,
    loading,
    error,
    clearResults: () => {
      setQuery('');
      setResults([]);
      setError(null);
    },
  };
}
