# Authentication & User Management

This document outlines the comprehensive authentication and user management system implemented in the Surprise Drink mobile app.

## Overview

The authentication system provides secure user registration, login, profile management, and password reset functionality with integration to WooCommerce customer management.

## Features Implemented

### ✅ Core Authentication
- **User Registration**: Create new accounts with WooCommerce customer integration
- **User Login**: Secure authentication with token-based sessions
- **User Logout**: Complete session cleanup and data removal
- **Session Persistence**: Automatic login restoration on app restart
- **Secure Token Storage**: Uses Expo SecureStore for token security

### ✅ Profile Management
- **Edit Profile**: Update user information (name, email)
- **Change Password**: Secure password update with validation
- **Profile Validation**: Form validation with real-time error feedback

### ✅ Password Recovery
- **Forgot Password**: Request password reset via email
- **Password Reset**: Secure password reset flow (simulated)

### ✅ Security Features
- **Secure Storage**: Tokens stored using Expo SecureStore
- **Form Validation**: Comprehensive input validation
- **Error Handling**: Robust error handling and user feedback
- **Session Management**: Automatic token refresh capability

## Architecture

### Services

#### AuthService (`src/services/auth.ts`)
Central authentication service handling all auth operations:
- Login/logout functionality
- User registration
- Profile updates
- Password management
- Token management
- WooCommerce integration

#### SecureStorage (`src/utils/secureStorage.ts`)
Secure storage utility that:
- Uses Expo SecureStore on native platforms
- Falls back to AsyncStorage on web
- Provides unified API for secure data storage

### Context Management

#### AuthContext (`src/context/AuthContext.tsx`)
React Context providing:
- Global authentication state
- Auth actions (login, logout, register, etc.)
- Loading states and error handling
- Session persistence

### Screens

#### Authentication Screens
1. **LoginScreen** - User login with email/password
2. **RegisterScreen** - New user registration
3. **ForgotPasswordScreen** - Password reset request

#### Profile Management Screens
1. **EditProfileScreen** - Update user profile information
2. **ChangePasswordScreen** - Change user password
3. **ProfileScreen** - Main profile screen with menu options

### Components

#### Enhanced Input Component (`src/components/common/Input.tsx`)
- Form validation integration
- Password visibility toggle
- Icon support
- Error state handling
- Accessibility features

#### Form Validation (`src/utils/validation.ts`)
- Reusable validation rules
- Real-time validation
- Custom validation hooks
- Error message management

## Usage Examples

### Basic Authentication Flow

```typescript
import { useAuth } from '../context/AuthContext';

const MyComponent = () => {
  const { login, register, logout, state } = useAuth();
  const { user, isAuthenticated, isLoading, error } = state;

  const handleLogin = async () => {
    try {
      await login({ email: '<EMAIL>', password: 'password' });
      // User is now logged in
    } catch (error) {
      // Handle login error
    }
  };

  return (
    <View>
      {isAuthenticated ? (
        <Text>Welcome, {user?.firstName}!</Text>
      ) : (
        <Button title="Login" onPress={handleLogin} />
      )}
    </View>
  );
};
```

### Form Validation

```typescript
import { useFormValidation, Validator } from '../utils/validation';

const LoginForm = () => {
  const {
    data,
    setValue,
    setFieldTouched,
    validateAll,
    getFieldError,
  } = useFormValidation(
    { email: '', password: '' },
    {
      email: [Validator.required(), Validator.email()],
      password: [Validator.required(), Validator.minLength(6)],
    }
  );

  return (
    <View>
      <Input
        label="Email"
        value={data.email}
        onChangeText={(text) => setValue('email', text)}
        onBlur={() => setFieldTouched('email')}
        error={getFieldError('email')}
        required
      />
    </View>
  );
};
```

### Protected Routes

```typescript
import { withAuth } from '../context/AuthContext';

const ProtectedScreen = withAuth(() => {
  return (
    <View>
      <Text>This screen requires authentication</Text>
    </View>
  );
});
```

## Security Considerations

### Token Storage
- Sensitive tokens stored in Expo SecureStore
- Automatic fallback to AsyncStorage on web
- Tokens cleared on logout

### Password Security
- Minimum password requirements enforced
- Password confirmation validation
- Secure password change flow

### Data Validation
- Client-side validation for user experience
- Server-side validation for security
- Input sanitization and error handling

### Session Management
- Automatic session restoration
- Token refresh capability
- Secure logout with data cleanup

## Testing

### Test Utilities (`src/utils/authTestHelpers.ts`)
Comprehensive testing utilities for:
- Authentication flow testing
- Profile management testing
- Error scenario testing
- Session state verification

### Running Tests

```typescript
import { testAuthFlow } from '../utils/authTestHelpers';

// Run all authentication tests
await testAuthFlow.runAllTests();

// Run individual tests
await testAuthFlow.testLogin();
await testAuthFlow.testRegistration();
```

## Integration with WooCommerce

### Customer Management
- New users automatically created as WooCommerce customers
- Profile updates sync with WooCommerce customer data
- Order history linked to customer account

### API Integration
- Uses WooCommerce REST API for customer operations
- Secure authentication with consumer key/secret
- Error handling for API failures

## Configuration

### Environment Setup
Update API configuration in `src/services/auth.ts`:

```typescript
// For production
this.baseURL = 'https://your-production-site.com';

// For development
this.baseURL = 'https://your-staging-site.com';
```

### Storage Keys
Defined in `src/constants/index.ts`:

```typescript
export const STORAGE_KEYS = {
  USER_TOKEN: '@user_token',
  USER_DATA: '@user_data',
  // ... other keys
};
```

## Future Enhancements

### Planned Features
- [ ] Social login (Google, Facebook, Apple)
- [ ] Two-factor authentication (2FA)
- [ ] Biometric authentication
- [ ] Account verification via email
- [ ] Advanced password policies
- [ ] Session timeout management

### Security Improvements
- [ ] JWT token implementation
- [ ] Refresh token rotation
- [ ] Device registration
- [ ] Suspicious activity detection
- [ ] Account lockout policies

## Troubleshooting

### Common Issues

1. **Login fails with valid credentials**
   - Check API endpoint configuration
   - Verify WooCommerce API credentials
   - Check network connectivity

2. **Session not persisting**
   - Verify SecureStore permissions
   - Check storage key consistency
   - Ensure proper context provider setup

3. **Form validation not working**
   - Check validation rules configuration
   - Verify form field names match validation keys
   - Ensure proper hook usage

### Debug Mode
Enable debug logging by setting `__DEV__` flag:

```typescript
if (__DEV__) {
  console.log('Auth debug info:', authService.getCurrentUser());
}
```

## Support

For authentication-related issues:
1. Check the console for error messages
2. Verify API connectivity
3. Test with the provided test utilities
4. Review the authentication flow documentation

---

This authentication system provides a solid foundation for user management in the Surprise Drink mobile app, with security best practices and extensibility for future enhancements.
