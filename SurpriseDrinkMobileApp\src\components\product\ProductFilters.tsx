import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Modal,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { WooCommerceCategory } from '../../types';
import { COLORS, FONTS, SPACING, BORDER_RADIUS, SHADOWS } from '../../constants';
import Button from '../common/Button';

export interface ProductFilters {
  category?: number;
  priceRange?: {
    min: number;
    max: number;
  };
  onSale?: boolean;
  featured?: boolean;
  inStock?: boolean;
  sortBy?: 'date' | 'price' | 'popularity' | 'rating' | 'title';
  sortOrder?: 'asc' | 'desc';
}

interface ProductFiltersProps {
  visible: boolean;
  onClose: () => void;
  onApply: (filters: ProductFilters) => void;
  categories: WooCommerceCategory[];
  currentFilters: ProductFilters;
}

const ProductFiltersComponent: React.FC<ProductFiltersProps> = ({
  visible,
  onClose,
  onApply,
  categories,
  currentFilters,
}) => {
  const [filters, setFilters] = useState<ProductFilters>(currentFilters);

  const sortOptions = [
    { key: 'date', label: 'Newest First', order: 'desc' },
    { key: 'date', label: 'Oldest First', order: 'asc' },
    { key: 'price', label: 'Price: Low to High', order: 'asc' },
    { key: 'price', label: 'Price: High to Low', order: 'desc' },
    { key: 'popularity', label: 'Most Popular', order: 'desc' },
    { key: 'rating', label: 'Highest Rated', order: 'desc' },
    { key: 'title', label: 'Name: A to Z', order: 'asc' },
    { key: 'title', label: 'Name: Z to A', order: 'desc' },
  ];

  const priceRanges = [
    { min: 0, max: 25, label: 'Under $25' },
    { min: 25, max: 50, label: '$25 - $50' },
    { min: 50, max: 100, label: '$50 - $100' },
    { min: 100, max: 200, label: '$100 - $200' },
    { min: 200, max: 999999, label: 'Over $200' },
  ];

  const updateFilter = (key: keyof ProductFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleApply = () => {
    onApply(filters);
    onClose();
  };

  const handleReset = () => {
    const resetFilters: ProductFilters = {};
    setFilters(resetFilters);
  };

  const renderSection = (title: string, children: React.ReactNode) => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {children}
    </View>
  );

  const renderCategoryFilter = () => (
    renderSection('Category', (
      <View style={styles.optionsContainer}>
        <TouchableOpacity
          style={[
            styles.option,
            !filters.category && styles.optionSelected,
          ]}
          onPress={() => updateFilter('category', undefined)}
        >
          <Text style={[
            styles.optionText,
            !filters.category && styles.optionTextSelected,
          ]}>
            All Categories
          </Text>
        </TouchableOpacity>
        {categories.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.option,
              filters.category === category.id && styles.optionSelected,
            ]}
            onPress={() => updateFilter('category', category.id)}
          >
            <Text style={[
              styles.optionText,
              filters.category === category.id && styles.optionTextSelected,
            ]}>
              {category.name} ({category.count})
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    ))
  );

  const renderPriceFilter = () => (
    renderSection('Price Range', (
      <View style={styles.optionsContainer}>
        <TouchableOpacity
          style={[
            styles.option,
            !filters.priceRange && styles.optionSelected,
          ]}
          onPress={() => updateFilter('priceRange', undefined)}
        >
          <Text style={[
            styles.optionText,
            !filters.priceRange && styles.optionTextSelected,
          ]}>
            Any Price
          </Text>
        </TouchableOpacity>
        {priceRanges.map((range, index) => {
          const isSelected = filters.priceRange?.min === range.min && 
                           filters.priceRange?.max === range.max;
          return (
            <TouchableOpacity
              key={index}
              style={[
                styles.option,
                isSelected && styles.optionSelected,
              ]}
              onPress={() => updateFilter('priceRange', range)}
            >
              <Text style={[
                styles.optionText,
                isSelected && styles.optionTextSelected,
              ]}>
                {range.label}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    ))
  );

  const renderSortFilter = () => (
    renderSection('Sort By', (
      <View style={styles.optionsContainer}>
        {sortOptions.map((option, index) => {
          const isSelected = filters.sortBy === option.key && 
                           filters.sortOrder === option.order;
          return (
            <TouchableOpacity
              key={index}
              style={[
                styles.option,
                isSelected && styles.optionSelected,
              ]}
              onPress={() => {
                updateFilter('sortBy', option.key);
                updateFilter('sortOrder', option.order);
              }}
            >
              <Text style={[
                styles.optionText,
                isSelected && styles.optionTextSelected,
              ]}>
                {option.label}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    ))
  );

  const renderToggleFilters = () => (
    renderSection('Product Type', (
      <View style={styles.toggleContainer}>
        <TouchableOpacity
          style={[
            styles.toggleOption,
            filters.onSale && styles.toggleOptionSelected,
          ]}
          onPress={() => updateFilter('onSale', !filters.onSale)}
        >
          <Ionicons
            name={filters.onSale ? 'checkmark-circle' : 'ellipse-outline'}
            size={20}
            color={filters.onSale ? COLORS.primary : COLORS.gray}
          />
          <Text style={[
            styles.toggleText,
            filters.onSale && styles.toggleTextSelected,
          ]}>
            On Sale
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.toggleOption,
            filters.featured && styles.toggleOptionSelected,
          ]}
          onPress={() => updateFilter('featured', !filters.featured)}
        >
          <Ionicons
            name={filters.featured ? 'checkmark-circle' : 'ellipse-outline'}
            size={20}
            color={filters.featured ? COLORS.primary : COLORS.gray}
          />
          <Text style={[
            styles.toggleText,
            filters.featured && styles.toggleTextSelected,
          ]}>
            Featured
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.toggleOption,
            filters.inStock && styles.toggleOptionSelected,
          ]}
          onPress={() => updateFilter('inStock', !filters.inStock)}
        >
          <Ionicons
            name={filters.inStock ? 'checkmark-circle' : 'ellipse-outline'}
            size={20}
            color={filters.inStock ? COLORS.primary : COLORS.gray}
          />
          <Text style={[
            styles.toggleText,
            filters.inStock && styles.toggleTextSelected,
          ]}>
            In Stock Only
          </Text>
        </TouchableOpacity>
      </View>
    ))
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={COLORS.textPrimary} />
          </TouchableOpacity>
          <Text style={styles.title}>Filters</Text>
          <TouchableOpacity onPress={handleReset} style={styles.resetButton}>
            <Text style={styles.resetText}>Reset</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {renderCategoryFilter()}
          {renderPriceFilter()}
          {renderSortFilter()}
          {renderToggleFilters()}
        </ScrollView>

        <View style={styles.footer}>
          <Button
            title="Apply Filters"
            onPress={handleApply}
            fullWidth
            size="large"
          />
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  closeButton: {
    padding: SPACING.sm,
  },
  title: {
    fontSize: FONTS.xl,
    fontWeight: '600',
    color: COLORS.textPrimary,
  },
  resetButton: {
    padding: SPACING.sm,
  },
  resetText: {
    fontSize: FONTS.md,
    color: COLORS.primary,
    fontWeight: '500',
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
  },
  section: {
    marginVertical: SPACING.lg,
  },
  sectionTitle: {
    fontSize: FONTS.lg,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: SPACING.md,
  },
  optionsContainer: {
    gap: SPACING.sm,
  },
  option: {
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    borderWidth: 1,
    borderColor: COLORS.border,
    backgroundColor: COLORS.white,
  },
  optionSelected: {
    borderColor: COLORS.primary,
    backgroundColor: COLORS.primaryLight,
  },
  optionText: {
    fontSize: FONTS.md,
    color: COLORS.textPrimary,
  },
  optionTextSelected: {
    color: COLORS.primary,
    fontWeight: '500',
  },
  toggleContainer: {
    gap: SPACING.md,
  },
  toggleOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
  },
  toggleOptionSelected: {
    // Additional styling for selected toggle if needed
  },
  toggleText: {
    fontSize: FONTS.md,
    color: COLORS.textPrimary,
    marginLeft: SPACING.sm,
  },
  toggleTextSelected: {
    color: COLORS.primary,
    fontWeight: '500',
  },
  footer: {
    padding: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    ...SHADOWS.light,
  },
});

export default ProductFiltersComponent;
