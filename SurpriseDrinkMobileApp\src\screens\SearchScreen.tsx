import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';

import { WooCommerceProduct, WooCommerceCategory } from '../types';
import { COLORS, FONTS, SPACING, BORDER_RADIUS, SHADOWS } from '../constants';
import { useApp } from '../context/AppContext';
import wooCommerceAPI from '../services/woocommerce';
import ProductGrid from '../components/product/ProductGrid';
import ProductFiltersComponent, { ProductFilters } from '../components/product/ProductFilters';
import { useSearchWithDebounce } from '../hooks/useLazyLoad';

const SearchScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { setError } = useApp();

  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<WooCommerceProduct[]>([]);
  const [categories, setCategories] = useState<WooCommerceCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [currentFilters, setCurrentFilters] = useState<ProductFilters>({});
  const [refreshing, setRefreshing] = useState(false);

  // Initialize with route params if any
  useEffect(() => {
    const params = route.params as any;
    if (params?.categoryId) {
      setCurrentFilters(prev => ({ ...prev, category: params.categoryId }));
      handleSearch('', { category: params.categoryId });
    }
    loadCategories();
  }, [route.params]);

  const loadCategories = async () => {
    try {
      const categoriesData = await wooCommerceAPI.getCategories();
      setCategories(categoriesData);
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  const handleSearch = async (query: string = searchQuery, filters: ProductFilters = currentFilters) => {
    try {
      setLoading(true);
      setError(null);

      const searchParams = {
        search: query.trim() || undefined,
        category: filters.category,
        featured: filters.featured,
        on_sale: filters.onSale,
        orderby: filters.sortBy,
        order: filters.sortOrder,
        per_page: 20,
      };

      const results = await wooCommerceAPI.getProducts(searchParams);
      setSearchResults(results);
      setHasSearched(true);
    } catch (error) {
      console.error('Error searching products:', error);
      setError('Failed to search products. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const navigateToProduct = (productId: number) => {
    navigation.navigate('ProductDetail', { productId });
  };

  const handleFiltersApply = (filters: ProductFilters) => {
    setCurrentFilters(filters);
    handleSearch(searchQuery, filters);
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await handleSearch();
    setRefreshing(false);
  };

  const clearSearch = () => {
    setSearchQuery('');
    setSearchResults([]);
    setHasSearched(false);
    setCurrentFilters({});
  };

  const getActiveFiltersCount = (): number => {
    let count = 0;
    if (currentFilters.category) count++;
    if (currentFilters.priceRange) count++;
    if (currentFilters.onSale) count++;
    if (currentFilters.featured) count++;
    if (currentFilters.inStock) count++;
    if (currentFilters.sortBy) count++;
    return count;
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color={COLORS.gray} style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search products..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          onSubmitEditing={() => handleSearch()}
          returnKeyType="search"
          autoFocus={!route.params}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity
            onPress={clearSearch}
            style={styles.clearButton}
          >
            <Ionicons name="close" size={20} color={COLORS.gray} />
          </TouchableOpacity>
        )}
      </View>

      <TouchableOpacity
        style={styles.filterButton}
        onPress={() => setShowFilters(true)}
      >
        <Ionicons name="options-outline" size={20} color={COLORS.primary} />
        {getActiveFiltersCount() > 0 && (
          <View style={styles.filterBadge}>
            <Text style={styles.filterBadgeText}>{getActiveFiltersCount()}</Text>
          </View>
        )}
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color={COLORS.gray} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search products..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            onSubmitEditing={() => handleSearch(searchQuery)}
            returnKeyType="search"
            autoFocus
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity
              onPress={() => {
                setSearchQuery('');
                setSearchResults([]);
                setHasSearched(false);
              }}
              style={styles.clearButton}
            >
              <Ionicons name="close" size={20} color={COLORS.gray} />
            </TouchableOpacity>
          )}
        </View>
        
        <TouchableOpacity
          style={styles.searchButton}
          onPress={() => handleSearch(searchQuery)}
        >
          <Text style={styles.searchButtonText}>Search</Text>
        </TouchableOpacity>
      </View>

      {searchResults.length > 0 ? (
        <FlatList
          data={searchResults}
          renderItem={renderProductItem}
          keyExtractor={(item) => item.id.toString()}
          numColumns={2}
          contentContainerStyle={styles.listContainer}
          columnWrapperStyle={styles.row}
          showsVerticalScrollIndicator={false}
        />
      ) : (
        renderEmptyState()
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    ...SHADOWS.light,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.lightGray,
    borderRadius: BORDER_RADIUS.md,
    paddingHorizontal: SPACING.md,
    marginRight: SPACING.md,
  },
  searchIcon: {
    marginRight: SPACING.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: FONTS.md,
    color: COLORS.textPrimary,
    paddingVertical: SPACING.md,
  },
  clearButton: {
    padding: SPACING.xs,
  },
  searchButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
  },
  searchButtonText: {
    color: COLORS.white,
    fontSize: FONTS.md,
    fontWeight: '600',
  },
  listContainer: {
    padding: SPACING.lg,
  },
  row: {
    justifyContent: 'space-between',
  },
  productCard: {
    marginBottom: SPACING.md,
  },
  leftCard: {
    marginRight: SPACING.sm,
  },
  rightCard: {
    marginLeft: SPACING.sm,
  },
  centerContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: SPACING.xl,
  },
  loadingText: {
    fontSize: FONTS.md,
    color: COLORS.textSecondary,
    marginTop: SPACING.md,
  },
  emptyTitle: {
    fontSize: FONTS.xl,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginTop: SPACING.lg,
    marginBottom: SPACING.sm,
  },
  emptySubtitle: {
    fontSize: FONTS.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
});

export default SearchScreen;
