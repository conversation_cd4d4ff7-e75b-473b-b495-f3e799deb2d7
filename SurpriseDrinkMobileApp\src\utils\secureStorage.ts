import * as SecureStore from 'expo-secure-store';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

// Secure storage utility that uses Expo SecureStore on native platforms
// and falls back to AsyncStorage on web
class SecureStorage {
  private isSecureStoreAvailable: boolean;

  constructor() {
    // SecureStore is available on iOS and Android, but not on web
    this.isSecureStoreAvailable = Platform.OS !== 'web';
  }

  async setItem(key: string, value: string): Promise<void> {
    try {
      if (this.isSecureStoreAvailable) {
        await SecureStore.setItemAsync(key, value);
      } else {
        await AsyncStorage.setItem(key, value);
      }
    } catch (error) {
      console.error('Error storing secure item:', error);
      throw error;
    }
  }

  async getItem(key: string): Promise<string | null> {
    try {
      if (this.isSecureStoreAvailable) {
        return await SecureStore.getItemAsync(key);
      } else {
        return await AsyncStorage.getItem(key);
      }
    } catch (error) {
      console.error('Error retrieving secure item:', error);
      return null;
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      if (this.isSecureStoreAvailable) {
        await SecureStore.deleteItemAsync(key);
      } else {
        await AsyncStorage.removeItem(key);
      }
    } catch (error) {
      console.error('Error removing secure item:', error);
      throw error;
    }
  }

  async clear(): Promise<void> {
    try {
      if (this.isSecureStoreAvailable) {
        // SecureStore doesn't have a clear method, so we need to remove items individually
        // For now, we'll just log that this operation is not supported
        console.warn('SecureStore clear operation not implemented');
      } else {
        await AsyncStorage.clear();
      }
    } catch (error) {
      console.error('Error clearing secure storage:', error);
      throw error;
    }
  }

  // Utility method to store JSON objects
  async setObject(key: string, value: any): Promise<void> {
    const jsonString = JSON.stringify(value);
    await this.setItem(key, jsonString);
  }

  // Utility method to retrieve JSON objects
  async getObject<T>(key: string): Promise<T | null> {
    const jsonString = await this.getItem(key);
    if (jsonString) {
      try {
        return JSON.parse(jsonString) as T;
      } catch (error) {
        console.error('Error parsing JSON from secure storage:', error);
        return null;
      }
    }
    return null;
  }

  // Check if an item exists
  async hasItem(key: string): Promise<boolean> {
    const value = await this.getItem(key);
    return value !== null;
  }

  // Get all keys (only works with AsyncStorage)
  async getAllKeys(): Promise<string[]> {
    try {
      if (!this.isSecureStoreAvailable) {
        return await AsyncStorage.getAllKeys();
      } else {
        console.warn('getAllKeys is not supported with SecureStore');
        return [];
      }
    } catch (error) {
      console.error('Error getting all keys:', error);
      return [];
    }
  }

  // Batch operations (only works with AsyncStorage)
  async multiSet(keyValuePairs: [string, string][]): Promise<void> {
    try {
      if (this.isSecureStoreAvailable) {
        // For SecureStore, we need to set items individually
        await Promise.all(
          keyValuePairs.map(([key, value]) => SecureStore.setItemAsync(key, value))
        );
      } else {
        await AsyncStorage.multiSet(keyValuePairs);
      }
    } catch (error) {
      console.error('Error in multiSet:', error);
      throw error;
    }
  }

  async multiGet(keys: string[]): Promise<[string, string | null][]> {
    try {
      if (this.isSecureStoreAvailable) {
        // For SecureStore, we need to get items individually
        const results = await Promise.all(
          keys.map(async (key) => {
            const value = await SecureStore.getItemAsync(key);
            return [key, value] as [string, string | null];
          })
        );
        return results;
      } else {
        return await AsyncStorage.multiGet(keys);
      }
    } catch (error) {
      console.error('Error in multiGet:', error);
      return keys.map(key => [key, null]);
    }
  }

  async multiRemove(keys: string[]): Promise<void> {
    try {
      if (this.isSecureStoreAvailable) {
        // For SecureStore, we need to remove items individually
        await Promise.all(
          keys.map(key => SecureStore.deleteItemAsync(key))
        );
      } else {
        await AsyncStorage.multiRemove(keys);
      }
    } catch (error) {
      console.error('Error in multiRemove:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export default new SecureStorage();

// Export specific methods for convenience
export const {
  setItem,
  getItem,
  removeItem,
  clear,
  setObject,
  getObject,
  hasItem,
  getAllKeys,
  multiSet,
  multiGet,
  multiRemove,
} = new SecureStorage();
