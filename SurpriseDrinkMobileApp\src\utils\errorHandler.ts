import { Alert } from 'react-native';
import { AxiosError } from 'axios';

export interface ApiError {
  message: string;
  code?: string;
  status?: number;
  data?: any;
}

export class ErrorHandler {
  static handleApiError(error: any): ApiError {
    console.error('API Error:', error);

    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;
      
      switch (status) {
        case 400:
          return {
            message: data?.message || 'Invalid request. Please check your input.',
            code: 'BAD_REQUEST',
            status,
            data,
          };
        case 401:
          return {
            message: 'Authentication failed. Please login again.',
            code: 'UNAUTHORIZED',
            status,
            data,
          };
        case 403:
          return {
            message: 'Access denied. You don\'t have permission to perform this action.',
            code: 'FORBIDDEN',
            status,
            data,
          };
        case 404:
          return {
            message: 'The requested resource was not found.',
            code: 'NOT_FOUND',
            status,
            data,
          };
        case 422:
          return {
            message: data?.message || 'Validation error. Please check your input.',
            code: 'VALIDATION_ERROR',
            status,
            data,
          };
        case 429:
          return {
            message: 'Too many requests. Please try again later.',
            code: 'RATE_LIMIT',
            status,
            data,
          };
        case 500:
          return {
            message: 'Server error. Please try again later.',
            code: 'SERVER_ERROR',
            status,
            data,
          };
        default:
          return {
            message: data?.message || `Server error (${status}). Please try again.`,
            code: 'UNKNOWN_SERVER_ERROR',
            status,
            data,
          };
      }
    } else if (error.request) {
      // Network error
      return {
        message: 'Network error. Please check your internet connection.',
        code: 'NETWORK_ERROR',
      };
    } else {
      // Other error
      return {
        message: error.message || 'An unexpected error occurred.',
        code: 'UNKNOWN_ERROR',
      };
    }
  }

  static showErrorAlert(error: ApiError, title: string = 'Error') {
    Alert.alert(title, error.message);
  }

  static handleError(error: any, showAlert: boolean = true): ApiError {
    const apiError = this.handleApiError(error);
    
    if (showAlert) {
      this.showErrorAlert(apiError);
    }
    
    return apiError;
  }

  static isNetworkError(error: ApiError): boolean {
    return error.code === 'NETWORK_ERROR';
  }

  static isAuthError(error: ApiError): boolean {
    return error.code === 'UNAUTHORIZED' || error.code === 'FORBIDDEN';
  }

  static isValidationError(error: ApiError): boolean {
    return error.code === 'VALIDATION_ERROR' || error.status === 422;
  }

  static getErrorMessage(error: any): string {
    const apiError = this.handleApiError(error);
    return apiError.message;
  }
}

// Utility functions for common error scenarios
export const handleNetworkError = () => {
  Alert.alert(
    'Connection Error',
    'Please check your internet connection and try again.',
    [{ text: 'OK' }]
  );
};

export const handleAuthError = (onRetry?: () => void) => {
  Alert.alert(
    'Authentication Required',
    'Please login to continue.',
    [
      { text: 'Cancel', style: 'cancel' },
      { text: 'Login', onPress: onRetry },
    ]
  );
};

export const handleValidationError = (errors: any) => {
  let message = 'Please check your input and try again.';
  
  if (typeof errors === 'object' && errors !== null) {
    const errorMessages = Object.values(errors).flat();
    if (errorMessages.length > 0) {
      message = errorMessages.join('\n');
    }
  } else if (typeof errors === 'string') {
    message = errors;
  }
  
  Alert.alert('Validation Error', message);
};

export const handleServerError = () => {
  Alert.alert(
    'Server Error',
    'We\'re experiencing technical difficulties. Please try again later.',
    [{ text: 'OK' }]
  );
};

// Retry utility
export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: any;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        throw error;
      }
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }
  
  throw lastError;
};

// Debounce utility for API calls
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};
