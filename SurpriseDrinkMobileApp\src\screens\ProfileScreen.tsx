import React from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

import { COLORS, FONTS, SPACING, BORDER_RADIUS, SHADOWS } from '../constants';
import { useApp } from '../context/AppContext';
import Button from '../components/common/Button';

const ProfileScreen: React.FC = () => {
  const navigation = useNavigation();
  const { state, setUser, clearCart, clearWishlist } = useApp();
  const { user } = state;

  const handleLogin = () => {
    navigation.navigate('Login');
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: () => {
            setUser(null);
            clearCart();
            clearWishlist();
          },
        },
      ]
    );
  };

  const menuItems = [
    {
      icon: 'receipt-outline',
      title: 'Order History',
      onPress: () => navigation.navigate('OrderHistory'),
      showArrow: true,
    },
    {
      icon: 'heart-outline',
      title: 'Wishlist',
      onPress: () => navigation.navigate('Wishlist'),
      showArrow: true,
    },
    {
      icon: 'location-outline',
      title: 'Addresses',
      onPress: () => Alert.alert('Coming Soon', 'This feature will be available soon.'),
      showArrow: true,
    },
    {
      icon: 'card-outline',
      title: 'Payment Methods',
      onPress: () => Alert.alert('Coming Soon', 'This feature will be available soon.'),
      showArrow: true,
    },
    {
      icon: 'settings-outline',
      title: 'Settings',
      onPress: () => Alert.alert('Coming Soon', 'This feature will be available soon.'),
      showArrow: true,
    },
    {
      icon: 'help-circle-outline',
      title: 'Help & Support',
      onPress: () => Alert.alert('Coming Soon', 'This feature will be available soon.'),
      showArrow: true,
    },
    {
      icon: 'information-circle-outline',
      title: 'About',
      onPress: () => Alert.alert('About', 'Surprise Drink Mobile App v1.0.0'),
      showArrow: true,
    },
  ];

  const renderMenuItem = (item: any, index: number) => (
    <TouchableOpacity
      key={index}
      style={styles.menuItem}
      onPress={item.onPress}
    >
      <View style={styles.menuItemLeft}>
        <Ionicons name={item.icon} size={24} color={COLORS.primary} />
        <Text style={styles.menuItemTitle}>{item.title}</Text>
      </View>
      {item.showArrow && (
        <Ionicons name="chevron-forward" size={20} color={COLORS.gray} />
      )}
    </TouchableOpacity>
  );

  const renderUserSection = () => {
    if (user) {
      return (
        <View style={styles.userSection}>
          <View style={styles.userAvatar}>
            <Ionicons name="person" size={40} color={COLORS.white} />
          </View>
          <View style={styles.userInfo}>
            <Text style={styles.userName}>
              {user.firstName} {user.lastName}
            </Text>
            <Text style={styles.userEmail}>{user.email}</Text>
          </View>
        </View>
      );
    }

    return (
      <View style={styles.guestSection}>
        <View style={styles.guestAvatar}>
          <Ionicons name="person-outline" size={40} color={COLORS.gray} />
        </View>
        <View style={styles.guestInfo}>
          <Text style={styles.guestTitle}>Welcome, Guest!</Text>
          <Text style={styles.guestSubtitle}>
            Sign in to access your account
          </Text>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Profile</Text>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderUserSection()}

        {!user && (
          <View style={styles.authButtons}>
            <Button
              title="Sign In"
              onPress={handleLogin}
              fullWidth
              style={styles.loginButton}
            />
            <Button
              title="Create Account"
              onPress={() => navigation.navigate('Register')}
              variant="outline"
              fullWidth
              style={styles.registerButton}
            />
          </View>
        )}

        <View style={styles.menuSection}>
          {menuItems.map((item, index) => renderMenuItem(item, index))}
        </View>

        {user && (
          <View style={styles.logoutSection}>
            <Button
              title="Logout"
              onPress={handleLogout}
              variant="outline"
              fullWidth
              style={styles.logoutButton}
            />
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    backgroundColor: COLORS.white,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    ...SHADOWS.light,
  },
  headerTitle: {
    fontSize: FONTS.xl,
    fontWeight: '600',
    color: COLORS.textPrimary,
  },
  scrollView: {
    flex: 1,
  },
  userSection: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    margin: SPACING.lg,
    padding: SPACING.lg,
    borderRadius: BORDER_RADIUS.md,
    ...SHADOWS.light,
  },
  userAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: COLORS.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  userInfo: {
    flex: 1,
    marginLeft: SPACING.md,
  },
  userName: {
    fontSize: FONTS.lg,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  userEmail: {
    fontSize: FONTS.md,
    color: COLORS.textSecondary,
  },
  guestSection: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    margin: SPACING.lg,
    padding: SPACING.lg,
    borderRadius: BORDER_RADIUS.md,
    ...SHADOWS.light,
  },
  guestAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: COLORS.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
  },
  guestInfo: {
    flex: 1,
    marginLeft: SPACING.md,
  },
  guestTitle: {
    fontSize: FONTS.lg,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  guestSubtitle: {
    fontSize: FONTS.md,
    color: COLORS.textSecondary,
  },
  authButtons: {
    paddingHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  loginButton: {
    marginBottom: SPACING.md,
  },
  registerButton: {
    borderColor: COLORS.primary,
  },
  menuSection: {
    backgroundColor: COLORS.white,
    marginHorizontal: SPACING.lg,
    borderRadius: BORDER_RADIUS.md,
    ...SHADOWS.light,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuItemTitle: {
    fontSize: FONTS.md,
    color: COLORS.textPrimary,
    marginLeft: SPACING.md,
  },
  logoutSection: {
    padding: SPACING.lg,
  },
  logoutButton: {
    borderColor: COLORS.error,
  },
});

export default ProfileScreen;
