import React from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../constants';
import { useApp } from '../context/AppContext';
import ProductCard from '../components/product/ProductCard';
import Button from '../components/common/Button';

const WishlistScreen: React.FC = () => {
  const navigation = useNavigation();
  const { state, clearWishlist } = useApp();
  const { wishlist } = state;

  const navigateToProduct = (productId: number) => {
    navigation.navigate('ProductDetail', { productId });
  };

  const renderWishlistItem = ({ item, index }: { item: any; index: number }) => (
    <ProductCard
      product={item}
      onPress={() => navigateToProduct(item.id)}
      style={[
        styles.productCard,
        index % 2 === 0 ? styles.leftCard : styles.rightCard,
      ]}
    />
  );

  const renderEmptyWishlist = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="heart-outline" size={80} color={COLORS.gray} />
      <Text style={styles.emptyTitle}>Your wishlist is empty</Text>
      <Text style={styles.emptySubtitle}>
        Add products you love to your wishlist
      </Text>
      <Button
        title="Start Shopping"
        onPress={() => navigation.navigate('Home')}
        style={styles.startShoppingButton}
      />
    </View>
  );

  if (wishlist.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Wishlist</Text>
        </View>
        {renderEmptyWishlist()}
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Wishlist ({wishlist.length})</Text>
        <TouchableOpacity onPress={clearWishlist}>
          <Text style={styles.clearText}>Clear All</Text>
        </TouchableOpacity>
      </View>

      <FlatList
        data={wishlist}
        renderItem={renderWishlistItem}
        keyExtractor={(item) => item.id.toString()}
        numColumns={2}
        contentContainerStyle={styles.listContainer}
        columnWrapperStyle={styles.row}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    backgroundColor: COLORS.white,
  },
  headerTitle: {
    fontSize: FONTS.xl,
    fontWeight: '600',
    color: COLORS.textPrimary,
  },
  clearText: {
    fontSize: FONTS.md,
    color: COLORS.error,
    fontWeight: '500',
  },
  listContainer: {
    padding: SPACING.lg,
  },
  row: {
    justifyContent: 'space-between',
  },
  productCard: {
    marginBottom: SPACING.md,
  },
  leftCard: {
    marginRight: SPACING.sm,
  },
  rightCard: {
    marginLeft: SPACING.sm,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: SPACING.xl,
  },
  emptyTitle: {
    fontSize: FONTS.xl,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginTop: SPACING.lg,
    marginBottom: SPACING.sm,
  },
  emptySubtitle: {
    fontSize: FONTS.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xl,
  },
  startShoppingButton: {
    paddingHorizontal: SPACING.xl,
  },
});

export default WishlistScreen;
