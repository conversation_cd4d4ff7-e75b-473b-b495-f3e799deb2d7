import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  Animated,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

import { COLORS, FONTS, SPACING, BORDER_RADIUS, SHADOWS } from '../constants';
import { useApp } from '../context/AppContext';
import { useAuth } from '../context/AuthContext';
import PremiumCard from '../components/common/PremiumCard';
import AnimatedButton from '../components/common/AnimatedButton';
import PremiumModal from '../components/common/PremiumModal';

interface ShippingAddress {
  firstName: string;
  lastName: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

interface PaymentMethod {
  cardNumber: string;
  expiryDate: string;
  cvv: string;
  cardholderName: string;
}

const EnhancedCheckoutScreen: React.FC = () => {
  const navigation = useNavigation();
  const { state, clearCart, getCartTotal } = useApp();
  const { state: authState } = useAuth();
  const { cart } = state;

  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [showOrderConfirmation, setShowOrderConfirmation] = useState(false);
  
  const [shippingAddress, setShippingAddress] = useState<ShippingAddress>({
    firstName: '',
    lastName: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'US',
  });

  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>({
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardholderName: '',
  });

  const progressAnim = useRef(new Animated.Value(0.33)).current;

  const updateProgress = (step: number) => {
    Animated.timing(progressAnim, {
      toValue: step / 3,
      duration: 300,
      useNativeDriver: false,
    }).start();
  };

  const handleNextStep = () => {
    if (currentStep < 3) {
      const nextStep = currentStep + 1;
      setCurrentStep(nextStep);
      updateProgress(nextStep);
    }
  };

  const handlePreviousStep = () => {
    if (currentStep > 1) {
      const prevStep = currentStep - 1;
      setCurrentStep(prevStep);
      updateProgress(prevStep);
    }
  };

  const validateShippingAddress = (): boolean => {
    const required = ['firstName', 'lastName', 'address', 'city', 'state', 'zipCode'];
    return required.every(field => shippingAddress[field as keyof ShippingAddress].trim() !== '');
  };

  const validatePaymentMethod = (): boolean => {
    const { cardNumber, expiryDate, cvv, cardholderName } = paymentMethod;
    return cardNumber.length >= 16 && expiryDate.length >= 5 && cvv.length >= 3 && cardholderName.trim() !== '';
  };

  const handlePlaceOrder = async () => {
    if (!validateShippingAddress() || !validatePaymentMethod()) {
      Alert.alert('Error', 'Please fill in all required fields.');
      return;
    }

    setLoading(true);
    
    try {
      // Simulate order processing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setLoading(false);
      setShowOrderConfirmation(true);
      
      // Clear cart after successful order
      setTimeout(() => {
        clearCart();
      }, 1000);
    } catch (error) {
      setLoading(false);
      Alert.alert('Error', 'Failed to place order. Please try again.');
    }
  };

  const renderProgressBar = () => (
    <View style={styles.progressContainer}>
      <View style={styles.progressBar}>
        <Animated.View
          style={[
            styles.progressFill,
            {
              width: progressAnim.interpolate({
                inputRange: [0, 1],
                outputRange: ['0%', '100%'],
              }),
            },
          ]}
        />
      </View>
      <View style={styles.stepIndicators}>
        {[1, 2, 3].map((step) => (
          <View
            key={step}
            style={[
              styles.stepIndicator,
              currentStep >= step && styles.activeStep,
            ]}
          >
            <Text style={[
              styles.stepText,
              currentStep >= step && styles.activeStepText,
            ]}>
              {step}
            </Text>
          </View>
        ))}
      </View>
    </View>
  );

  const renderShippingForm = () => (
    <PremiumCard style={styles.formCard} padding="large">
      <Text style={styles.sectionTitle}>Shipping Address</Text>
      
      <View style={styles.row}>
        <TextInput
          style={[styles.input, styles.halfInput]}
          placeholder="First Name"
          value={shippingAddress.firstName}
          onChangeText={(text) => setShippingAddress(prev => ({ ...prev, firstName: text }))}
        />
        <TextInput
          style={[styles.input, styles.halfInput]}
          placeholder="Last Name"
          value={shippingAddress.lastName}
          onChangeText={(text) => setShippingAddress(prev => ({ ...prev, lastName: text }))}
        />
      </View>
      
      <TextInput
        style={styles.input}
        placeholder="Address"
        value={shippingAddress.address}
        onChangeText={(text) => setShippingAddress(prev => ({ ...prev, address: text }))}
      />
      
      <TextInput
        style={styles.input}
        placeholder="City"
        value={shippingAddress.city}
        onChangeText={(text) => setShippingAddress(prev => ({ ...prev, city: text }))}
      />
      
      <View style={styles.row}>
        <TextInput
          style={[styles.input, styles.halfInput]}
          placeholder="State"
          value={shippingAddress.state}
          onChangeText={(text) => setShippingAddress(prev => ({ ...prev, state: text }))}
        />
        <TextInput
          style={[styles.input, styles.halfInput]}
          placeholder="ZIP Code"
          value={shippingAddress.zipCode}
          onChangeText={(text) => setShippingAddress(prev => ({ ...prev, zipCode: text }))}
        />
      </View>
    </PremiumCard>
  );

  const renderPaymentForm = () => (
    <PremiumCard style={styles.formCard} padding="large">
      <Text style={styles.sectionTitle}>Payment Information</Text>
      
      <TextInput
        style={styles.input}
        placeholder="Cardholder Name"
        value={paymentMethod.cardholderName}
        onChangeText={(text) => setPaymentMethod(prev => ({ ...prev, cardholderName: text }))}
      />
      
      <TextInput
        style={styles.input}
        placeholder="Card Number"
        value={paymentMethod.cardNumber}
        onChangeText={(text) => setPaymentMethod(prev => ({ ...prev, cardNumber: text }))}
        keyboardType="numeric"
        maxLength={19}
      />
      
      <View style={styles.row}>
        <TextInput
          style={[styles.input, styles.halfInput]}
          placeholder="MM/YY"
          value={paymentMethod.expiryDate}
          onChangeText={(text) => setPaymentMethod(prev => ({ ...prev, expiryDate: text }))}
          maxLength={5}
        />
        <TextInput
          style={[styles.input, styles.halfInput]}
          placeholder="CVV"
          value={paymentMethod.cvv}
          onChangeText={(text) => setPaymentMethod(prev => ({ ...prev, cvv: text }))}
          keyboardType="numeric"
          maxLength={4}
          secureTextEntry
        />
      </View>
    </PremiumCard>
  );

  const renderOrderSummary = () => {
    const subtotal = getCartTotal();
    const shipping = subtotal > 50 ? 0 : 9.99;
    const tax = subtotal * 0.08;
    const total = subtotal + shipping + tax;

    return (
      <PremiumCard style={styles.summaryCard} padding="large">
        <Text style={styles.sectionTitle}>Order Summary</Text>
        
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Items ({cart.length})</Text>
          <Text style={styles.summaryValue}>${subtotal.toFixed(2)}</Text>
        </View>
        
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Shipping</Text>
          <Text style={[styles.summaryValue, shipping === 0 && styles.freeShipping]}>
            {shipping === 0 ? 'FREE' : `$${shipping.toFixed(2)}`}
          </Text>
        </View>
        
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Tax</Text>
          <Text style={styles.summaryValue}>${tax.toFixed(2)}</Text>
        </View>
        
        <View style={[styles.summaryRow, styles.totalRow]}>
          <Text style={styles.totalLabel}>Total</Text>
          <Text style={styles.totalValue}>${total.toFixed(2)}</Text>
        </View>
      </PremiumCard>
    );
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return renderShippingForm();
      case 2:
        return renderPaymentForm();
      case 3:
        return renderOrderSummary();
      default:
        return null;
    }
  };

  const renderNavigationButtons = () => (
    <View style={styles.navigationContainer}>
      {currentStep > 1 && (
        <AnimatedButton
          title="Previous"
          onPress={handlePreviousStep}
          variant="outline"
          size="large"
          style={styles.navButton}
        />
      )}
      
      {currentStep < 3 ? (
        <AnimatedButton
          title="Next"
          onPress={handleNextStep}
          variant="primary"
          size="large"
          style={[styles.navButton, currentStep === 1 && styles.fullWidthButton]}
          disabled={currentStep === 1 ? !validateShippingAddress() : !validatePaymentMethod()}
        />
      ) : (
        <AnimatedButton
          title="Place Order"
          onPress={handlePlaceOrder}
          variant="primary"
          size="large"
          style={[styles.navButton, currentStep === 1 && styles.fullWidthButton]}
          loading={loading}
          icon={<Ionicons name="card-outline" size={20} color={COLORS.white} />}
        />
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color={COLORS.textPrimary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Checkout</Text>
          <View style={{ width: 24 }} />
        </View>

        {renderProgressBar()}

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {renderStepContent()}
        </ScrollView>

        {renderNavigationButtons()}

        <PremiumModal
          visible={showOrderConfirmation}
          onClose={() => {
            setShowOrderConfirmation(false);
            navigation.navigate('Home');
          }}
          size="medium"
          animationType="scale"
        >
          <View style={styles.confirmationContent}>
            <Ionicons name="checkmark-circle" size={80} color={COLORS.success} />
            <Text style={styles.confirmationTitle}>Order Placed!</Text>
            <Text style={styles.confirmationMessage}>
              Thank you for your order. You will receive a confirmation email shortly.
            </Text>
            <AnimatedButton
              title="Continue Shopping"
              onPress={() => {
                setShowOrderConfirmation(false);
                navigation.navigate('Home');
              }}
              variant="primary"
              size="large"
              style={styles.confirmationButton}
            />
          </View>
        </PremiumModal>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    backgroundColor: COLORS.white,
    ...SHADOWS.light,
  },
  headerTitle: {
    fontSize: FONTS.xl,
    fontWeight: '600',
    color: COLORS.textPrimary,
  },
  progressContainer: {
    padding: SPACING.lg,
    backgroundColor: COLORS.white,
  },
  progressBar: {
    height: 4,
    backgroundColor: COLORS.lightGray,
    borderRadius: 2,
    marginBottom: SPACING.md,
  },
  progressFill: {
    height: '100%',
    backgroundColor: COLORS.primary,
    borderRadius: 2,
  },
  stepIndicators: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  stepIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeStep: {
    backgroundColor: COLORS.primary,
  },
  stepText: {
    fontSize: FONTS.sm,
    fontWeight: '600',
    color: COLORS.textSecondary,
  },
  activeStepText: {
    color: COLORS.white,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  formCard: {
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    fontSize: FONTS.lg,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: SPACING.lg,
  },
  input: {
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: BORDER_RADIUS.md,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    fontSize: FONTS.md,
    color: COLORS.textPrimary,
    backgroundColor: COLORS.white,
    marginBottom: SPACING.md,
  },
  row: {
    flexDirection: 'row',
    gap: SPACING.md,
  },
  halfInput: {
    flex: 1,
  },
  summaryCard: {
    marginBottom: SPACING.lg,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  summaryLabel: {
    fontSize: FONTS.md,
    color: COLORS.textSecondary,
  },
  summaryValue: {
    fontSize: FONTS.md,
    fontWeight: '500',
    color: COLORS.textPrimary,
  },
  freeShipping: {
    color: COLORS.success,
    fontWeight: '600',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    paddingTop: SPACING.sm,
    marginTop: SPACING.sm,
  },
  totalLabel: {
    fontSize: FONTS.lg,
    fontWeight: '600',
    color: COLORS.textPrimary,
  },
  totalValue: {
    fontSize: FONTS.lg,
    fontWeight: '700',
    color: COLORS.primary,
  },
  navigationContainer: {
    flexDirection: 'row',
    padding: SPACING.lg,
    backgroundColor: COLORS.white,
    gap: SPACING.md,
    ...SHADOWS.light,
  },
  navButton: {
    flex: 1,
  },
  fullWidthButton: {
    flex: 1,
  },
  confirmationContent: {
    alignItems: 'center',
    paddingVertical: SPACING.xl,
  },
  confirmationTitle: {
    fontSize: FONTS.xxl,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginTop: SPACING.lg,
    marginBottom: SPACING.md,
  },
  confirmationMessage: {
    fontSize: FONTS.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xl,
    lineHeight: 22,
  },
  confirmationButton: {
    marginTop: SPACING.lg,
  },
  orderNumber: {
    fontSize: FONTS.lg,
    fontWeight: '600',
    color: COLORS.primary,
    marginBottom: SPACING.lg,
  },
});

export default EnhancedCheckoutScreen;
