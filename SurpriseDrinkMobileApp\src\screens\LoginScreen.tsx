import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';

import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../constants';
import { useAuth } from '../context/AuthContext';
import Button from '../components/common/Button';
import Input from '../components/common/Input';
import { useFormValidation, Validator } from '../utils/validation';
import LoadingSpinner from '../components/common/LoadingSpinner';

const LoginScreen: React.FC = () => {
  const navigation = useNavigation();
  const { login, state, clearError } = useAuth();

  const {
    data,
    setValue,
    setFieldTouched,
    validateAll,
    getFieldError,
    isValid,
  } = useFormValidation(
    { email: '', password: '' },
    {
      email: [Validator.required(), Validator.email()],
      password: [Validator.required(), Validator.minLength(6)],
    }
  );

  const handleLogin = async () => {
    if (!validateAll()) {
      Alert.alert('Error', 'Please fix the errors and try again');
      return;
    }

    try {
      clearError();
      await login({
        email: data.email,
        password: data.password,
      });

      Alert.alert('Success', 'Login successful!', [
        { text: 'OK', onPress: () => navigation.goBack() }
      ]);
    } catch (error: any) {
      Alert.alert('Login Failed', error.message || 'Please try again.');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.content}>
          <Text style={styles.title}>Welcome Back</Text>
          <Text style={styles.subtitle}>Sign in to your account</Text>

          <View style={styles.form}>
            <Input
              label="Email"
              value={data.email}
              onChangeText={(text) => setValue('email', text)}
              onBlur={() => setFieldTouched('email')}
              placeholder="Enter your email"
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
              leftIcon="mail-outline"
              error={getFieldError('email')}
              required
            />

            <Input
              label="Password"
              value={data.password}
              onChangeText={(text) => setValue('password', text)}
              onBlur={() => setFieldTouched('password')}
              placeholder="Enter your password"
              secureTextEntry
              autoCapitalize="none"
              autoCorrect={false}
              leftIcon="lock-closed-outline"
              showPasswordToggle
              error={getFieldError('password')}
              required
            />

            <TouchableOpacity
              style={styles.forgotPassword}
              onPress={() => navigation.navigate('ForgotPassword')}
            >
              <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
            </TouchableOpacity>

            <Button
              title="Sign In"
              onPress={handleLogin}
              fullWidth
              size="large"
              style={styles.loginButton}
              loading={state.isLoading}
              disabled={state.isLoading}
            />

            <View style={styles.signupContainer}>
              <Text style={styles.signupText}>Don't have an account? </Text>
              <TouchableOpacity onPress={() => navigation.navigate('Register')}>
                <Text style={styles.signupLink}>Sign Up</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
    justifyContent: 'center',
  },
  title: {
    fontSize: FONTS.xxxl,
    fontWeight: '700',
    color: COLORS.textPrimary,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  subtitle: {
    fontSize: FONTS.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xl,
  },
  form: {
    width: '100%',
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginBottom: SPACING.xl,
  },
  forgotPasswordText: {
    fontSize: FONTS.md,
    color: COLORS.primary,
    fontWeight: '500',
  },
  loginButton: {
    marginBottom: SPACING.lg,
  },
  signupContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  signupText: {
    fontSize: FONTS.md,
    color: COLORS.textSecondary,
  },
  signupLink: {
    fontSize: FONTS.md,
    color: COLORS.primary,
    fontWeight: '600',
  },
});

export default LoginScreen;
