import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { CartItem } from '../../types';
import { COLORS, FONTS, SPACING, BORDER_RADIUS, SHADOWS } from '../../constants';
import CachedImage from '../common/CachedImage';
import PremiumCard from '../common/PremiumCard';

interface CartItemCardProps {
  item: CartItem;
  onQuantityChange: (productId: number, newQuantity: number) => void;
  onRemove: (productId: number) => void;
  isRemoving?: boolean;
  style?: any;
}

const CartItemCard: React.FC<CartItemCardProps> = ({
  item,
  onQuantityChange,
  onRemove,
  isRemoving = false,
  style,
}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    if (isRemoving) {
      Animated.parallel([
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [isRemoving, scaleAnim, opacityAnim]);

  const handleQuantityDecrease = () => {
    if (item.quantity > 1) {
      onQuantityChange(item.product.id, item.quantity - 1);
    } else {
      handleRemove();
    }
  };

  const handleQuantityIncrease = () => {
    onQuantityChange(item.product.id, item.quantity + 1);
  };

  const handleRemove = () => {
    Alert.alert(
      'Remove Item',
      `Remove ${item.product.name} from your cart?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => onRemove(item.product.id),
        },
      ]
    );
  };

  const getProductImage = () => {
    if (item.product.images && item.product.images.length > 0) {
      return item.product.images[0].src;
    }
    return 'https://via.placeholder.com/80x80/E91E63/FFFFFF?text=Product';
  };

  const calculateItemTotal = () => {
    return (parseFloat(item.product.price) * item.quantity).toFixed(2);
  };

  const isOnSale = item.product.on_sale && 
    item.product.regular_price && 
    parseFloat(item.product.regular_price) > parseFloat(item.product.price);

  return (
    <Animated.View
      style={[
        style,
        {
          transform: [{ scale: scaleAnim }],
          opacity: opacityAnim,
        },
      ]}
    >
      <PremiumCard
        style={styles.container}
        padding="medium"
        shadow="light"
        animated={true}
      >
        <View style={styles.content}>
          {/* Product Image */}
          <View style={styles.imageContainer}>
            <CachedImage
              uri={getProductImage()}
              style={styles.productImage}
              placeholder="https://via.placeholder.com/80x80"
              showLoader={true}
              fallbackIcon="image-outline"
            />
            {isOnSale && (
              <View style={styles.saleBadge}>
                <Text style={styles.saleBadgeText}>SALE</Text>
              </View>
            )}
          </View>

          {/* Product Details */}
          <View style={styles.details}>
            <Text style={styles.productName} numberOfLines={2}>
              {item.product.name}
            </Text>
            
            <View style={styles.priceContainer}>
              {isOnSale && (
                <Text style={styles.originalPrice}>
                  ${item.product.regular_price}
                </Text>
              )}
              <Text style={styles.currentPrice}>
                ${item.product.price}
              </Text>
            </View>

            {/* Quantity Controls */}
            <View style={styles.quantityContainer}>
              <TouchableOpacity
                style={[
                  styles.quantityButton,
                  item.quantity === 1 && styles.removeButton,
                ]}
                onPress={handleQuantityDecrease}
              >
                <Ionicons
                  name={item.quantity === 1 ? "trash-outline" : "remove"}
                  size={16}
                  color={item.quantity === 1 ? COLORS.error : COLORS.primary}
                />
              </TouchableOpacity>
              
              <View style={styles.quantityDisplay}>
                <Text style={styles.quantity}>{item.quantity}</Text>
              </View>
              
              <TouchableOpacity
                style={styles.quantityButton}
                onPress={handleQuantityIncrease}
              >
                <Ionicons name="add" size={16} color={COLORS.primary} />
              </TouchableOpacity>
            </View>
          </View>

          {/* Item Total and Actions */}
          <View style={styles.actions}>
            <Text style={styles.itemTotal}>
              ${calculateItemTotal()}
            </Text>
            
            <TouchableOpacity
              style={styles.removeIconButton}
              onPress={handleRemove}
            >
              <Ionicons name="close-circle" size={24} color={COLORS.error} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Stock Status */}
        {item.product.stock_status === 'outofstock' && (
          <View style={styles.outOfStockBanner}>
            <Text style={styles.outOfStockText}>Out of Stock</Text>
          </View>
        )}
      </PremiumCard>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.md,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  imageContainer: {
    position: 'relative',
  },
  productImage: {
    width: 80,
    height: 80,
    borderRadius: BORDER_RADIUS.md,
  },
  saleBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: COLORS.error,
    borderRadius: BORDER_RADIUS.sm,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  saleBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: COLORS.white,
  },
  details: {
    flex: 1,
    marginLeft: SPACING.md,
    marginRight: SPACING.sm,
  },
  productName: {
    fontSize: FONTS.md,
    fontWeight: '500',
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
    lineHeight: 20,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  originalPrice: {
    fontSize: FONTS.sm,
    color: COLORS.textSecondary,
    textDecorationLine: 'line-through',
    marginRight: SPACING.sm,
  },
  currentPrice: {
    fontSize: FONTS.md,
    fontWeight: '600',
    color: COLORS.primary,
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  removeButton: {
    backgroundColor: COLORS.error + '10',
    borderColor: COLORS.error + '30',
  },
  quantityDisplay: {
    minWidth: 40,
    alignItems: 'center',
    marginHorizontal: SPACING.sm,
  },
  quantity: {
    fontSize: FONTS.md,
    fontWeight: '600',
    color: COLORS.textPrimary,
  },
  actions: {
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    minHeight: 80,
  },
  itemTotal: {
    fontSize: FONTS.lg,
    fontWeight: '700',
    color: COLORS.textPrimary,
  },
  removeIconButton: {
    padding: SPACING.xs,
  },
  outOfStockBanner: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.error,
    paddingVertical: SPACING.xs,
    alignItems: 'center',
    borderTopLeftRadius: BORDER_RADIUS.md,
    borderTopRightRadius: BORDER_RADIUS.md,
  },
  outOfStockText: {
    fontSize: FONTS.sm,
    fontWeight: '600',
    color: COLORS.white,
  },
});

export default CartItemCard;
