# Testing Guide

Comprehensive testing strategy and implementation guide for the Surprise Drink mobile app.

## 🧪 Testing Strategy

### Testing Pyramid

1. **Unit Tests (70%)**
   - Individual functions and components
   - Business logic and utilities
   - API service methods
   - Custom hooks

2. **Integration Tests (20%)**
   - Component interactions
   - Context providers
   - API integrations
   - Navigation flows

3. **E2E Tests (10%)**
   - Critical user journeys
   - Cross-platform compatibility
   - Performance testing
   - Accessibility testing

## 🛠 Testing Setup

### Dependencies

```json
{
  "devDependencies": {
    "@testing-library/react-native": "^12.0.0",
    "@testing-library/jest-native": "^5.4.0",
    "jest": "^29.0.0",
    "jest-expo": "^50.0.0",
    "react-test-renderer": "^18.0.0",
    "detox": "^20.0.0",
    "@types/jest": "^29.0.0"
  }
}
```

### Jest Configuration

```javascript
// jest.config.js
module.exports = {
  preset: 'jest-expo',
  setupFilesAfterEnv: [
    '@testing-library/jest-native/extend-expect',
    '<rootDir>/src/test-utils/setup.ts'
  ],
  testMatch: [
    '**/__tests__/**/*.(ts|tsx|js)',
    '**/*.(test|spec).(ts|tsx|js)'
  ],
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/test-utils/**',
    '!src/**/__tests__/**'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  }
};
```

## 📝 Unit Testing

### Component Testing

```typescript
// ProductCard.test.tsx
import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import ProductCard from '../ProductCard';
import { mockProduct } from '../../test-utils/mocks';

describe('ProductCard', () => {
  const mockOnPress = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders product information correctly', () => {
    const { getByText } = render(
      <ProductCard product={mockProduct} onPress={mockOnPress} />
    );

    expect(getByText(mockProduct.name)).toBeTruthy();
    expect(getByText(`$${mockProduct.price}`)).toBeTruthy();
  });

  it('calls onPress when tapped', () => {
    const { getByTestId } = render(
      <ProductCard product={mockProduct} onPress={mockOnPress} />
    );

    fireEvent.press(getByTestId('product-card'));
    expect(mockOnPress).toHaveBeenCalledWith(mockProduct.id);
  });

  it('shows sale badge when product is on sale', () => {
    const saleProduct = { ...mockProduct, on_sale: true };
    const { getByText } = render(
      <ProductCard product={saleProduct} onPress={mockOnPress} />
    );

    expect(getByText('SALE')).toBeTruthy();
  });
});
```

### Hook Testing

```typescript
// useCart.test.ts
import { renderHook, act } from '@testing-library/react-native';
import { useCart } from '../useCart';
import { mockProduct } from '../../test-utils/mocks';

describe('useCart', () => {
  it('adds item to cart', () => {
    const { result } = renderHook(() => useCart());

    act(() => {
      result.current.addToCart(mockProduct, 2);
    });

    expect(result.current.cart).toHaveLength(1);
    expect(result.current.cart[0].quantity).toBe(2);
  });

  it('updates item quantity', () => {
    const { result } = renderHook(() => useCart());

    act(() => {
      result.current.addToCart(mockProduct, 1);
      result.current.updateQuantity(mockProduct.id, 3);
    });

    expect(result.current.cart[0].quantity).toBe(3);
  });

  it('removes item from cart', () => {
    const { result } = renderHook(() => useCart());

    act(() => {
      result.current.addToCart(mockProduct, 1);
      result.current.removeFromCart(mockProduct.id);
    });

    expect(result.current.cart).toHaveLength(0);
  });
});
```

### Service Testing

```typescript
// woocommerce.test.ts
import wooCommerceAPI from '../woocommerce';
import { mockProducts, mockCategories } from '../../test-utils/mocks';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('WooCommerce API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getProducts', () => {
    it('fetches products successfully', async () => {
      mockedAxios.get.mockResolvedValueOnce({ data: mockProducts });

      const products = await wooCommerceAPI.getProducts();

      expect(products).toEqual(mockProducts);
      expect(mockedAxios.get).toHaveBeenCalledWith('/products', {
        params: expect.any(Object)
      });
    });

    it('handles API errors gracefully', async () => {
      mockedAxios.get.mockRejectedValueOnce(new Error('Network error'));

      await expect(wooCommerceAPI.getProducts()).rejects.toThrow('Network error');
    });
  });
});
```

## 🔗 Integration Testing

### Context Provider Testing

```typescript
// AppContext.test.tsx
import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { AppProvider, useApp } from '../AppContext';
import { mockProduct } from '../../test-utils/mocks';

const TestComponent = () => {
  const { state, addToCart } = useApp();
  
  return (
    <View>
      <Text testID="cart-count">{state.cart.length}</Text>
      <Button
        testID="add-to-cart"
        title="Add to Cart"
        onPress={() => addToCart(mockProduct, 1)}
      />
    </View>
  );
};

describe('AppContext Integration', () => {
  it('manages cart state correctly', () => {
    const { getByTestId } = render(
      <AppProvider>
        <TestComponent />
      </AppProvider>
    );

    expect(getByTestId('cart-count')).toHaveTextContent('0');

    fireEvent.press(getByTestId('add-to-cart'));

    expect(getByTestId('cart-count')).toHaveTextContent('1');
  });
});
```

### Navigation Testing

```typescript
// Navigation.test.tsx
import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { render, fireEvent } from '@testing-library/react-native';
import AppNavigator from '../AppNavigator';

const renderWithNavigation = (component: React.ReactElement) => {
  return render(
    <NavigationContainer>
      {component}
    </NavigationContainer>
  );
};

describe('Navigation Integration', () => {
  it('navigates to product detail screen', () => {
    const { getByTestId } = renderWithNavigation(<AppNavigator />);

    fireEvent.press(getByTestId('product-card-1'));

    expect(getByTestId('product-detail-screen')).toBeTruthy();
  });
});
```

## 🎭 E2E Testing with Detox

### Detox Configuration

```json
// .detoxrc.json
{
  "testRunner": "jest",
  "runnerConfig": "e2e/config.json",
  "configurations": {
    "ios.sim.debug": {
      "binaryPath": "ios/build/Build/Products/Debug-iphonesimulator/SurpriseDrink.app",
      "build": "xcodebuild -workspace ios/SurpriseDrink.xcworkspace -scheme SurpriseDrink -configuration Debug -sdk iphonesimulator -derivedDataPath ios/build",
      "type": "ios.simulator",
      "device": {
        "type": "iPhone 14"
      }
    },
    "android.emu.debug": {
      "binaryPath": "android/app/build/outputs/apk/debug/app-debug.apk",
      "build": "cd android && ./gradlew assembleDebug assembleAndroidTest -DtestBuildType=debug",
      "type": "android.emulator",
      "device": {
        "avdName": "Pixel_4_API_30"
      }
    }
  }
}
```

### E2E Test Examples

```typescript
// e2e/shopping-flow.e2e.ts
describe('Shopping Flow', () => {
  beforeAll(async () => {
    await device.launchApp();
  });

  beforeEach(async () => {
    await device.reloadReactNative();
  });

  it('should complete a purchase flow', async () => {
    // Navigate to product
    await element(by.id('product-card-1')).tap();
    await expect(element(by.id('product-detail-screen'))).toBeVisible();

    // Add to cart
    await element(by.id('add-to-cart-button')).tap();
    await expect(element(by.text('Added to cart'))).toBeVisible();

    // Go to cart
    await element(by.id('cart-tab')).tap();
    await expect(element(by.id('cart-screen'))).toBeVisible();

    // Proceed to checkout
    await element(by.id('checkout-button')).tap();
    await expect(element(by.id('checkout-screen'))).toBeVisible();

    // Fill shipping information
    await element(by.id('first-name-input')).typeText('John');
    await element(by.id('last-name-input')).typeText('Doe');
    await element(by.id('address-input')).typeText('123 Main St');

    // Complete purchase
    await element(by.id('place-order-button')).tap();
    await expect(element(by.text('Order placed successfully'))).toBeVisible();
  });
});
```

## 🎯 Test Utilities

### Mock Data

```typescript
// test-utils/mocks.ts
export const mockProduct: WooCommerceProduct = {
  id: 1,
  name: 'Test Product',
  price: '29.99',
  regular_price: '39.99',
  sale_price: '29.99',
  on_sale: true,
  stock_status: 'instock',
  images: [
    {
      id: 1,
      src: 'https://example.com/image.jpg',
      alt: 'Test Product Image'
    }
  ],
  categories: [
    {
      id: 1,
      name: 'Test Category'
    }
  ]
};

export const mockUser = {
  id: 1,
  username: 'testuser',
  email: '<EMAIL>',
  display_name: 'Test User'
};
```

### Test Providers

```typescript
// test-utils/providers.tsx
import React from 'react';
import { AppProvider } from '../context/AppContext';
import { AuthProvider } from '../context/AuthContext';

export const TestProviders: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <AuthProvider>
      <AppProvider>
        {children}
      </AppProvider>
    </AuthProvider>
  );
};

export const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <TestProviders>
      {component}
    </TestProviders>
  );
};
```

## 📊 Test Coverage

### Coverage Reports

```bash
# Generate coverage report
npm run test:coverage

# View coverage in browser
npm run test:coverage:open
```

### Coverage Goals

- **Statements**: 85%
- **Branches**: 80%
- **Functions**: 85%
- **Lines**: 85%

### Critical Areas (100% Coverage Required)

- Payment processing logic
- Authentication flows
- Cart management
- Order processing
- Data validation

## 🚀 Continuous Integration

### GitHub Actions Workflow

```yaml
# .github/workflows/test.yml
name: Test Suite

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - run: npm ci
      - run: npm run lint
      - run: npm run type-check
      - run: npm run test:coverage
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
```

## 🔍 Testing Best Practices

### Do's

- ✅ Test behavior, not implementation
- ✅ Use descriptive test names
- ✅ Keep tests simple and focused
- ✅ Mock external dependencies
- ✅ Test error scenarios
- ✅ Use data-testid for reliable element selection

### Don'ts

- ❌ Test implementation details
- ❌ Write overly complex tests
- ❌ Ignore test failures
- ❌ Skip edge cases
- ❌ Test third-party libraries
- ❌ Use production data in tests

### Test Organization

```
__tests__/
├── components/
│   ├── common/
│   └── product/
├── hooks/
├── services/
├── utils/
└── integration/
```

## 🐛 Debugging Tests

### Common Issues

1. **Async Operations**
   ```typescript
   // Use waitFor for async operations
   await waitFor(() => {
     expect(getByText('Loaded')).toBeTruthy();
   });
   ```

2. **Timer Issues**
   ```typescript
   // Mock timers when needed
   jest.useFakeTimers();
   // ... test code
   jest.runAllTimers();
   jest.useRealTimers();
   ```

3. **Navigation Issues**
   ```typescript
   // Mock navigation
   const mockNavigate = jest.fn();
   jest.mock('@react-navigation/native', () => ({
     useNavigation: () => ({ navigate: mockNavigate })
   }));
   ```

---

**Remember**: Good tests are an investment in code quality and developer confidence. Write tests that provide value and catch real bugs.
