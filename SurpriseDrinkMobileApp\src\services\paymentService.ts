import { CartItem } from '../types';

export interface PaymentMethod {
  id: string;
  type: 'card' | 'paypal' | 'apple_pay' | 'google_pay';
  cardNumber?: string;
  expiryDate?: string;
  cvv?: string;
  cardholderName?: string;
  last4?: string;
  brand?: string;
}

export interface ShippingAddress {
  firstName: string;
  lastName: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  phone?: string;
}

export interface OrderData {
  items: CartItem[];
  shippingAddress: ShippingAddress;
  paymentMethod: PaymentMethod;
  subtotal: number;
  shipping: number;
  tax: number;
  total: number;
}

export interface OrderResult {
  success: boolean;
  orderId?: string;
  error?: string;
  transactionId?: string;
}

class PaymentService {
  private apiKey: string = 'demo_key'; // In production, this would be from environment variables

  // Validate credit card number using <PERSON>hn algorithm
  validateCardNumber(cardNumber: string): boolean {
    const cleanNumber = cardNumber.replace(/\s/g, '');
    
    if (!/^\d+$/.test(cleanNumber) || cleanNumber.length < 13 || cleanNumber.length > 19) {
      return false;
    }

    let sum = 0;
    let isEven = false;

    for (let i = cleanNumber.length - 1; i >= 0; i--) {
      let digit = parseInt(cleanNumber[i]);

      if (isEven) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }

      sum += digit;
      isEven = !isEven;
    }

    return sum % 10 === 0;
  }

  // Validate expiry date
  validateExpiryDate(expiryDate: string): boolean {
    const match = expiryDate.match(/^(\d{2})\/(\d{2})$/);
    if (!match) return false;

    const month = parseInt(match[1]);
    const year = parseInt(match[2]) + 2000;

    if (month < 1 || month > 12) return false;

    const now = new Date();
    const expiry = new Date(year, month - 1);
    
    return expiry > now;
  }

  // Validate CVV
  validateCVV(cvv: string, cardType?: string): boolean {
    if (!/^\d+$/.test(cvv)) return false;
    
    // American Express uses 4 digits, others use 3
    const expectedLength = cardType === 'amex' ? 4 : 3;
    return cvv.length === expectedLength;
  }

  // Get card type from number
  getCardType(cardNumber: string): string {
    const cleanNumber = cardNumber.replace(/\s/g, '');
    
    if (/^4/.test(cleanNumber)) return 'visa';
    if (/^5[1-5]/.test(cleanNumber)) return 'mastercard';
    if (/^3[47]/.test(cleanNumber)) return 'amex';
    if (/^6(?:011|5)/.test(cleanNumber)) return 'discover';
    
    return 'unknown';
  }

  // Format card number for display
  formatCardNumber(cardNumber: string): string {
    const cleanNumber = cardNumber.replace(/\s/g, '');
    const cardType = this.getCardType(cleanNumber);
    
    if (cardType === 'amex') {
      return cleanNumber.replace(/(\d{4})(\d{6})(\d{5})/, '$1 $2 $3');
    } else {
      return cleanNumber.replace(/(\d{4})/g, '$1 ').trim();
    }
  }

  // Mask card number for security
  maskCardNumber(cardNumber: string): string {
    const cleanNumber = cardNumber.replace(/\s/g, '');
    const last4 = cleanNumber.slice(-4);
    return `**** **** **** ${last4}`;
  }

  // Calculate shipping cost
  calculateShipping(subtotal: number, shippingAddress: ShippingAddress): number {
    // Free shipping over $50
    if (subtotal >= 50) return 0;
    
    // Different rates based on location
    const domesticRate = 9.99;
    const internationalRate = 19.99;
    
    return shippingAddress.country === 'US' ? domesticRate : internationalRate;
  }

  // Calculate tax
  calculateTax(subtotal: number, shippingAddress: ShippingAddress): number {
    // Tax rates by state (simplified)
    const taxRates: { [key: string]: number } = {
      'CA': 0.0875, // California
      'NY': 0.08,   // New York
      'TX': 0.0625, // Texas
      'FL': 0.06,   // Florida
      'WA': 0.065,  // Washington
    };
    
    const rate = taxRates[shippingAddress.state] || 0.05; // Default 5%
    return subtotal * rate;
  }

  // Process payment (mock implementation)
  async processPayment(orderData: OrderData): Promise<OrderResult> {
    try {
      // Validate payment method
      if (orderData.paymentMethod.type === 'card') {
        const { cardNumber, expiryDate, cvv } = orderData.paymentMethod;
        
        if (!cardNumber || !this.validateCardNumber(cardNumber)) {
          return { success: false, error: 'Invalid card number' };
        }
        
        if (!expiryDate || !this.validateExpiryDate(expiryDate)) {
          return { success: false, error: 'Invalid expiry date' };
        }
        
        if (!cvv || !this.validateCVV(cvv, this.getCardType(cardNumber))) {
          return { success: false, error: 'Invalid CVV' };
        }
      }

      // Simulate payment processing delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Simulate random payment failures (10% chance)
      if (Math.random() < 0.1) {
        return { 
          success: false, 
          error: 'Payment declined. Please try a different payment method.' 
        };
      }

      // Generate mock order ID and transaction ID
      const orderId = `SD${Date.now()}${Math.floor(Math.random() * 1000)}`;
      const transactionId = `TXN${Date.now()}${Math.floor(Math.random() * 10000)}`;

      return {
        success: true,
        orderId,
        transactionId,
      };
    } catch (error) {
      console.error('Payment processing error:', error);
      return {
        success: false,
        error: 'Payment processing failed. Please try again.',
      };
    }
  }

  // Create order in WooCommerce (mock implementation)
  async createOrder(orderData: OrderData): Promise<OrderResult> {
    try {
      // In a real implementation, this would call the WooCommerce API
      // to create an order with the provided data
      
      const orderPayload = {
        payment_method: 'stripe', // or other payment gateway
        payment_method_title: 'Credit Card',
        set_paid: true,
        billing: {
          first_name: orderData.shippingAddress.firstName,
          last_name: orderData.shippingAddress.lastName,
          address_1: orderData.shippingAddress.address,
          city: orderData.shippingAddress.city,
          state: orderData.shippingAddress.state,
          postcode: orderData.shippingAddress.zipCode,
          country: orderData.shippingAddress.country,
        },
        shipping: {
          first_name: orderData.shippingAddress.firstName,
          last_name: orderData.shippingAddress.lastName,
          address_1: orderData.shippingAddress.address,
          city: orderData.shippingAddress.city,
          state: orderData.shippingAddress.state,
          postcode: orderData.shippingAddress.zipCode,
          country: orderData.shippingAddress.country,
        },
        line_items: orderData.items.map(item => ({
          product_id: item.product.id,
          quantity: item.quantity,
        })),
        shipping_lines: [
          {
            method_id: 'flat_rate',
            method_title: 'Flat Rate',
            total: orderData.shipping.toString(),
          },
        ],
      };

      console.log('Creating order with payload:', orderPayload);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const orderId = `WC${Date.now()}`;
      
      return {
        success: true,
        orderId,
      };
    } catch (error) {
      console.error('Order creation error:', error);
      return {
        success: false,
        error: 'Failed to create order. Please try again.',
      };
    }
  }

  // Complete checkout process
  async completeCheckout(orderData: OrderData): Promise<OrderResult> {
    try {
      // Step 1: Process payment
      const paymentResult = await this.processPayment(orderData);
      
      if (!paymentResult.success) {
        return paymentResult;
      }

      // Step 2: Create order in WooCommerce
      const orderResult = await this.createOrder(orderData);
      
      if (!orderResult.success) {
        // In a real implementation, you might want to refund the payment here
        return orderResult;
      }

      return {
        success: true,
        orderId: orderResult.orderId,
        transactionId: paymentResult.transactionId,
      };
    } catch (error) {
      console.error('Checkout completion error:', error);
      return {
        success: false,
        error: 'Checkout failed. Please try again.',
      };
    }
  }
}

export default new PaymentService();
