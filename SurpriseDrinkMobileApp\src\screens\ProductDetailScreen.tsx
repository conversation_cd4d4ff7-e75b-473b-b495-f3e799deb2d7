import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Image,
  TouchableOpacity,
  Dimensions,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useRoute, useNavigation } from '@react-navigation/native';

import { WooCommerceProduct } from '../types';
import { COLORS, FONTS, SPACING, BORDER_RADIUS, SHADOWS, PLACEHOLDER_IMAGES } from '../constants';
import { useApp } from '../context/AppContext';
import wooCommerceAPI from '../services/woocommerce';
import Button from '../components/common/Button';

const { width } = Dimensions.get('window');

const ProductDetailScreen: React.FC = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { productId } = route.params as { productId: number };
  const { addToCart, addToWishlist, removeFromWishlist, isInWishlist, setLoading, setError } = useApp();

  const [product, setProduct] = useState<WooCommerceProduct | null>(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);

  useEffect(() => {
    loadProduct();
  }, [productId]);

  const loadProduct = async () => {
    try {
      setLoading(true);
      setError(null);
      const productData = await wooCommerceAPI.getProduct(productId);
      setProduct(productData);
    } catch (error) {
      console.error('Error loading product:', error);
      setError('Failed to load product details.');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const handleAddToCart = () => {
    if (product) {
      addToCart(product, quantity);
      Alert.alert('Success', 'Product added to cart!');
    }
  };

  const handleWishlistToggle = () => {
    if (product) {
      if (isInWishlist(product.id)) {
        removeFromWishlist(product.id);
      } else {
        addToWishlist(product);
      }
    }
  };

  const formatPrice = (price: string): string => {
    const numPrice = parseFloat(price);
    return `$${numPrice.toFixed(2)}`;
  };

  if (!product) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const images = product.images && product.images.length > 0 
    ? product.images 
    : [{ src: PLACEHOLDER_IMAGES.PRODUCT, alt: product.name }];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Product Images */}
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: images[selectedImageIndex].src }}
            style={styles.mainImage}
            resizeMode="cover"
          />
          <TouchableOpacity
            style={styles.wishlistButton}
            onPress={handleWishlistToggle}
          >
            <Ionicons
              name={isInWishlist(product.id) ? 'heart' : 'heart-outline'}
              size={24}
              color={isInWishlist(product.id) ? COLORS.error : COLORS.gray}
            />
          </TouchableOpacity>
        </View>

        {/* Product Info */}
        <View style={styles.productInfo}>
          <Text style={styles.productName}>{product.name}</Text>
          
          {product.categories && product.categories.length > 0 && (
            <Text style={styles.category}>{product.categories[0].name}</Text>
          )}

          <View style={styles.priceContainer}>
            {product.on_sale ? (
              <>
                <Text style={styles.salePrice}>{formatPrice(product.sale_price)}</Text>
                <Text style={styles.regularPrice}>{formatPrice(product.regular_price)}</Text>
              </>
            ) : (
              <Text style={styles.price}>{formatPrice(product.price)}</Text>
            )}
          </View>

          {product.short_description && (
            <Text style={styles.description}>{product.short_description}</Text>
          )}

          {/* Quantity Selector */}
          <View style={styles.quantityContainer}>
            <Text style={styles.quantityLabel}>Quantity:</Text>
            <View style={styles.quantitySelector}>
              <TouchableOpacity
                style={styles.quantityButton}
                onPress={() => setQuantity(Math.max(1, quantity - 1))}
              >
                <Ionicons name="remove" size={20} color={COLORS.primary} />
              </TouchableOpacity>
              <Text style={styles.quantityText}>{quantity}</Text>
              <TouchableOpacity
                style={styles.quantityButton}
                onPress={() => setQuantity(quantity + 1)}
              >
                <Ionicons name="add" size={20} color={COLORS.primary} />
              </TouchableOpacity>
            </View>
          </View>

          {/* Stock Status */}
          <View style={styles.stockContainer}>
            <Ionicons
              name={product.stock_status === 'instock' ? 'checkmark-circle' : 'close-circle'}
              size={16}
              color={product.stock_status === 'instock' ? COLORS.success : COLORS.error}
            />
            <Text style={[
              styles.stockText,
              { color: product.stock_status === 'instock' ? COLORS.success : COLORS.error }
            ]}>
              {product.stock_status === 'instock' ? 'In Stock' : 'Out of Stock'}
            </Text>
          </View>
        </View>
      </ScrollView>

      {/* Add to Cart Button */}
      <View style={styles.bottomContainer}>
        <Button
          title={`Add to Cart • ${formatPrice((parseFloat(product.price) * quantity).toString())}`}
          onPress={handleAddToCart}
          disabled={product.stock_status !== 'instock'}
          fullWidth
          size="large"
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  imageContainer: {
    position: 'relative',
    width: width,
    height: width,
  },
  mainImage: {
    width: '100%',
    height: '100%',
  },
  wishlistButton: {
    position: 'absolute',
    top: SPACING.lg,
    right: SPACING.lg,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: COLORS.white,
    alignItems: 'center',
    justifyContent: 'center',
    ...SHADOWS.medium,
  },
  productInfo: {
    padding: SPACING.lg,
  },
  productName: {
    fontSize: FONTS.xxl,
    fontWeight: '700',
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
    lineHeight: FONTS.lineHeight.xxl,
  },
  category: {
    fontSize: FONTS.md,
    color: COLORS.primary,
    fontWeight: '500',
    marginBottom: SPACING.md,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  price: {
    fontSize: FONTS.xxl,
    fontWeight: '700',
    color: COLORS.primary,
  },
  salePrice: {
    fontSize: FONTS.xxl,
    fontWeight: '700',
    color: COLORS.error,
    marginRight: SPACING.md,
  },
  regularPrice: {
    fontSize: FONTS.lg,
    color: COLORS.textSecondary,
    textDecorationLine: 'line-through',
  },
  description: {
    fontSize: FONTS.md,
    color: COLORS.textSecondary,
    lineHeight: FONTS.lineHeight.md,
    marginBottom: SPACING.lg,
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  quantityLabel: {
    fontSize: FONTS.md,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginRight: SPACING.md,
  },
  quantitySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.lightGray,
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.xs,
  },
  quantityButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: COLORS.white,
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityText: {
    fontSize: FONTS.md,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginHorizontal: SPACING.lg,
    minWidth: 30,
    textAlign: 'center',
  },
  stockContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  stockText: {
    fontSize: FONTS.md,
    fontWeight: '500',
    marginLeft: SPACING.sm,
  },
  bottomContainer: {
    backgroundColor: COLORS.white,
    padding: SPACING.lg,
    ...SHADOWS.medium,
  },
});

export default ProductDetailScreen;
