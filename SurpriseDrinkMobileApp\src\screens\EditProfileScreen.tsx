import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';

import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../constants';
import { useAuth } from '../context/AuthContext';
import Button from '../components/common/Button';
import Input from '../components/common/Input';
import { useFormValidation, Validator } from '../utils/validation';

const EditProfileScreen: React.FC = () => {
  const navigation = useNavigation();
  const { state, updateProfile, clearError } = useAuth();
  const { user } = state;

  const {
    data,
    setValue,
    setFieldTouched,
    validateAll,
    getFieldError,
    reset,
  } = useFormValidation(
    { 
      firstName: '', 
      lastName: '', 
      email: '' 
    },
    {
      firstName: [Validator.required('First name is required')],
      lastName: [Validator.required('Last name is required')],
      email: [Validator.required(), Validator.email()],
    }
  );

  // Initialize form with user data
  useEffect(() => {
    if (user) {
      setValue('firstName', user.firstName);
      setValue('lastName', user.lastName);
      setValue('email', user.email);
    }
  }, [user, setValue]);

  const handleSaveProfile = async () => {
    if (!validateAll()) {
      Alert.alert('Error', 'Please fix the errors and try again');
      return;
    }

    try {
      clearError();
      await updateProfile({
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
      });
      
      Alert.alert('Success', 'Profile updated successfully!', [
        { text: 'OK', onPress: () => navigation.goBack() }
      ]);
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to update profile.');
    }
  };

  const handleCancel = () => {
    if (user) {
      setValue('firstName', user.firstName);
      setValue('lastName', user.lastName);
      setValue('email', user.email);
    }
    navigation.goBack();
  };

  if (!user) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>User not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <View style={styles.content}>
            <Text style={styles.title}>Edit Profile</Text>
            <Text style={styles.subtitle}>Update your personal information</Text>

            <View style={styles.form}>
              <Input
                label="First Name"
                value={data.firstName}
                onChangeText={(text) => setValue('firstName', text)}
                onBlur={() => setFieldTouched('firstName')}
                placeholder="Enter your first name"
                autoCapitalize="words"
                leftIcon="person-outline"
                error={getFieldError('firstName')}
                required
              />

              <Input
                label="Last Name"
                value={data.lastName}
                onChangeText={(text) => setValue('lastName', text)}
                onBlur={() => setFieldTouched('lastName')}
                placeholder="Enter your last name"
                autoCapitalize="words"
                leftIcon="person-outline"
                error={getFieldError('lastName')}
                required
              />

              <Input
                label="Email"
                value={data.email}
                onChangeText={(text) => setValue('email', text)}
                onBlur={() => setFieldTouched('email')}
                placeholder="Enter your email"
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
                leftIcon="mail-outline"
                error={getFieldError('email')}
                required
              />

              <View style={styles.buttonContainer}>
                <Button
                  title="Save Changes"
                  onPress={handleSaveProfile}
                  fullWidth
                  size="large"
                  style={styles.saveButton}
                  loading={state.isLoading}
                  disabled={state.isLoading}
                />

                <Button
                  title="Cancel"
                  onPress={handleCancel}
                  variant="outline"
                  fullWidth
                  size="large"
                  style={styles.cancelButton}
                  disabled={state.isLoading}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  keyboardView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
    paddingTop: SPACING.xl,
  },
  title: {
    fontSize: FONTS.xxl,
    fontWeight: '700',
    color: COLORS.textPrimary,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  subtitle: {
    fontSize: FONTS.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xl,
  },
  form: {
    width: '100%',
  },
  buttonContainer: {
    marginTop: SPACING.lg,
  },
  saveButton: {
    marginBottom: SPACING.md,
  },
  cancelButton: {
    borderColor: COLORS.gray,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    fontSize: FONTS.lg,
    color: COLORS.error,
    textAlign: 'center',
  },
});

export default EditProfileScreen;
