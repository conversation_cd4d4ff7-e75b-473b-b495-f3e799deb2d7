import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { WooCommerceProduct } from '../../types';
import { COLORS, FONTS, SPACING, BORDER_RADIUS, SHADOWS, PLACEHOLDER_IMAGES } from '../../constants';
import { useApp } from '../../context/AppContext';
import { useAuth } from '../../context/AuthContext';
import Button from '../common/Button';
import CachedImage from '../common/CachedImage';

const { width } = Dimensions.get('window');
const CARD_WIDTH = (width - SPACING.lg * 3) / 2;

interface ProductCardProps {
  product: WooCommerceProduct;
  onPress: () => void;
  showAddToCart?: boolean;
  showQuickView?: boolean;
  layout?: 'grid' | 'list';
  style?: any;
}

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  onPress,
  showAddToCart = true,
  style,
}) => {
  const { addToCart, addToWishlist, removeFromWishlist, isInWishlist } = useApp();
  const { state: authState } = useAuth();

  const handleAddToCart = () => {
    addToCart(product, 1);
    Alert.alert('Success', `${product.name} added to cart!`);
  };

  const handleWishlistToggle = () => {
    if (!authState.isAuthenticated) {
      Alert.alert('Login Required', 'Please login to add items to your wishlist.');
      return;
    }

    if (isInWishlist(product.id)) {
      removeFromWishlist(product.id);
      Alert.alert('Removed', `${product.name} removed from wishlist.`);
    } else {
      addToWishlist(product);
      Alert.alert('Added', `${product.name} added to wishlist!`);
    }
  };

  const getDiscountPercentage = (): number => {
    if (!product.on_sale) return 0;
    const regular = parseFloat(product.regular_price);
    const sale = parseFloat(product.sale_price);
    return Math.round(((regular - sale) / regular) * 100);
  };

  const formatPrice = (price: string): string => {
    const numPrice = parseFloat(price);
    return `$${numPrice.toFixed(2)}`;
  };

  const getProductImage = (): string => {
    if (product.images && product.images.length > 0) {
      return product.images[0].src;
    }
    return PLACEHOLDER_IMAGES.PRODUCT;
  };

  const renderPriceSection = () => {
    if (product.on_sale) {
      return (
        <View style={styles.priceContainer}>
          <Text style={styles.salePrice}>{formatPrice(product.sale_price)}</Text>
          <Text style={styles.regularPrice}>{formatPrice(product.regular_price)}</Text>
        </View>
      );
    }

    return (
      <View style={styles.priceContainer}>
        <Text style={styles.price}>{formatPrice(product.price)}</Text>
      </View>
    );
  };

  const renderBadges = () => {
    const badges = [];

    if (product.on_sale) {
      const discount = getDiscountPercentage();
      badges.push(
        <View key="sale" style={[styles.badge, styles.saleBadge]}>
          <Text style={styles.badgeText}>-{discount}%</Text>
        </View>
      );
    }

    if (product.featured) {
      badges.push(
        <View key="featured" style={[styles.badge, styles.featuredBadge]}>
          <Text style={styles.badgeText}>Featured</Text>
        </View>
      );
    }

    if (product.stock_status === 'outofstock') {
      badges.push(
        <View key="outofstock" style={[styles.badge, styles.outOfStockBadge]}>
          <Text style={styles.badgeText}>Out of Stock</Text>
        </View>
      );
    }

    return badges.length > 0 ? (
      <View style={styles.badgesContainer}>{badges}</View>
    ) : null;
  };

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={onPress}
      activeOpacity={0.9}
    >
      <View style={styles.imageContainer}>
        <CachedImage
          uri={getProductImage()}
          style={styles.image}
          placeholder={PLACEHOLDER_IMAGES.PRODUCT}
          showLoader={true}
          fallbackIcon="image-outline"
        />
        {renderBadges()}
        <TouchableOpacity
          style={styles.wishlistButton}
          onPress={handleWishlistToggle}
          activeOpacity={0.7}
        >
          <Ionicons
            name={isInWishlist(product.id) ? 'heart' : 'heart-outline'}
            size={20}
            color={isInWishlist(product.id) ? COLORS.error : COLORS.gray}
          />
        </TouchableOpacity>
      </View>

      <View style={styles.contentContainer}>
        <Text style={styles.productName} numberOfLines={2}>
          {product.name}
        </Text>

        {product.categories && product.categories.length > 0 && (
          <Text style={styles.category} numberOfLines={1}>
            {product.categories[0].name}
          </Text>
        )}

        {renderPriceSection()}

        {product.average_rating && parseFloat(product.average_rating) > 0 && (
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={14} color={COLORS.warning} />
            <Text style={styles.rating}>
              {parseFloat(product.average_rating).toFixed(1)} ({product.rating_count})
            </Text>
          </View>
        )}

        {showAddToCart && product.stock_status === 'instock' && (
          <Button
            title="Add to Cart"
            onPress={handleAddToCart}
            size="small"
            fullWidth
            style={styles.addToCartButton}
          />
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: CARD_WIDTH,
    backgroundColor: COLORS.white,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.md,
    ...SHADOWS.light,
  },
  imageContainer: {
    position: 'relative',
    width: '100%',
    height: CARD_WIDTH * 0.8,
    borderTopLeftRadius: BORDER_RADIUS.md,
    borderTopRightRadius: BORDER_RADIUS.md,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  badgesContainer: {
    position: 'absolute',
    top: SPACING.sm,
    left: SPACING.sm,
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  badge: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.xs,
    marginBottom: SPACING.xs,
  },
  saleBadge: {
    backgroundColor: COLORS.error,
  },
  featuredBadge: {
    backgroundColor: COLORS.primary,
  },
  outOfStockBadge: {
    backgroundColor: COLORS.gray,
  },
  badgeText: {
    color: COLORS.white,
    fontSize: FONTS.xs,
    fontWeight: '600',
  },
  wishlistButton: {
    position: 'absolute',
    top: SPACING.sm,
    right: SPACING.sm,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.white,
    alignItems: 'center',
    justifyContent: 'center',
    ...SHADOWS.light,
  },
  contentContainer: {
    padding: SPACING.md,
  },
  productName: {
    fontSize: FONTS.md,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
    lineHeight: FONTS.lineHeight.md,
  },
  category: {
    fontSize: FONTS.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.sm,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  price: {
    fontSize: FONTS.lg,
    fontWeight: '700',
    color: COLORS.primary,
  },
  salePrice: {
    fontSize: FONTS.lg,
    fontWeight: '700',
    color: COLORS.error,
    marginRight: SPACING.sm,
  },
  regularPrice: {
    fontSize: FONTS.md,
    color: COLORS.textSecondary,
    textDecorationLine: 'line-through',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  rating: {
    fontSize: FONTS.sm,
    color: COLORS.textSecondary,
    marginLeft: SPACING.xs,
  },
  addToCartButton: {
    marginTop: SPACING.sm,
  },
});

export default ProductCard;
