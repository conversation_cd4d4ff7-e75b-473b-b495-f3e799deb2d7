import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Appearance, ColorSchemeName } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { THEME } from '../theme';

// Theme types
export type ThemeMode = 'light' | 'dark' | 'auto';

interface ThemeColors {
  primary: string;
  secondary: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  error: string;
  success: string;
  warning: string;
  info: string;
}

interface ThemeContextType {
  theme: typeof THEME;
  colors: ThemeColors;
  mode: ThemeMode;
  isDark: boolean;
  setThemeMode: (mode: ThemeMode) => void;
  toggleTheme: () => void;
}

// Create context
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Storage key
const THEME_STORAGE_KEY = '@theme_mode';

// Dark theme colors
const DARK_COLORS: ThemeColors = {
  primary: '#E91E63',
  secondary: '#9C27B0',
  background: '#121212',
  surface: '#1E1E1E',
  text: '#FFFFFF',
  textSecondary: '#B3B3B3',
  border: '#333333',
  error: '#CF6679',
  success: '#4CAF50',
  warning: '#FF9800',
  info: '#2196F3',
};

// Light theme colors
const LIGHT_COLORS: ThemeColors = {
  primary: '#E91E63',
  secondary: '#9C27B0',
  background: '#FAFAFA',
  surface: '#FFFFFF',
  text: '#212121',
  textSecondary: '#757575',
  border: '#E0E0E0',
  error: '#F44336',
  success: '#4CAF50',
  warning: '#FF9800',
  info: '#2196F3',
};

// Theme provider component
interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [mode, setMode] = useState<ThemeMode>('auto');
  const [systemColorScheme, setSystemColorScheme] = useState<ColorSchemeName>(
    Appearance.getColorScheme()
  );

  // Load saved theme mode on mount
  useEffect(() => {
    loadThemeMode();
    
    // Listen for system theme changes
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      setSystemColorScheme(colorScheme);
    });

    return () => subscription?.remove();
  }, []);

  const loadThemeMode = async () => {
    try {
      const savedMode = await AsyncStorage.getItem(THEME_STORAGE_KEY);
      if (savedMode && ['light', 'dark', 'auto'].includes(savedMode)) {
        setMode(savedMode as ThemeMode);
      }
    } catch (error) {
      console.error('Error loading theme mode:', error);
    }
  };

  const saveThemeMode = async (newMode: ThemeMode) => {
    try {
      await AsyncStorage.setItem(THEME_STORAGE_KEY, newMode);
    } catch (error) {
      console.error('Error saving theme mode:', error);
    }
  };

  const setThemeMode = (newMode: ThemeMode) => {
    setMode(newMode);
    saveThemeMode(newMode);
  };

  const toggleTheme = () => {
    const newMode = isDark ? 'light' : 'dark';
    setThemeMode(newMode);
  };

  // Determine if dark mode should be active
  const isDark = mode === 'dark' || (mode === 'auto' && systemColorScheme === 'dark');

  // Get current colors based on theme
  const colors = isDark ? DARK_COLORS : LIGHT_COLORS;

  // Create theme object with current colors
  const theme = {
    ...THEME,
    colors: {
      ...THEME.colors,
      ...colors,
    },
  };

  const contextValue: ThemeContextType = {
    theme,
    colors,
    mode,
    isDark,
    setThemeMode,
    toggleTheme,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook to use theme
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// HOC for theme-aware components
export const withTheme = <P extends object>(
  Component: React.ComponentType<P & { theme: typeof THEME }>
): React.FC<P> => {
  return (props: P) => {
    const { theme } = useTheme();
    return <Component {...props} theme={theme} />;
  };
};

// Utility functions
export const createThemedStyles = <T extends Record<string, any>>(
  stylesFn: (theme: typeof THEME) => T
) => {
  return (theme: typeof THEME): T => stylesFn(theme);
};

// Theme-aware styled components helper
export const styled = {
  create: <T extends Record<string, any>>(
    stylesFn: (theme: typeof THEME) => T
  ) => {
    return (theme: typeof THEME) => stylesFn(theme);
  },
};

export default ThemeContext;
