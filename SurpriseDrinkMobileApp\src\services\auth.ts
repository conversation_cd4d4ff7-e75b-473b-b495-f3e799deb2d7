import AsyncStorage from '@react-native-async-storage/async-storage';
import axios, { AxiosResponse } from 'axios';
import { User, WooCommerceCustomer } from '../types';
import { STORAGE_KEYS, API_CONFIG } from '../constants';
import { <PERSON><PERSON>r<PERSON>and<PERSON> } from '../utils/errorHandler';
import secureStorage from '../utils/secureStorage';
import wooCommerceAPI from './woocommerce';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  phone?: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken?: string;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordResetConfirm {
  email: string;
  code: string;
  newPassword: string;
}

class AuthService {
  private baseURL: string;
  private currentUser: User | null = null;

  constructor() {
    this.baseURL = API_CONFIG.BASE_URL;
  }

  // Initialize auth service - check for stored user
  async initialize(): Promise<User | null> {
    try {
      const userData = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);
      const token = await secureStorage.getItem(STORAGE_KEYS.USER_TOKEN);

      if (userData && token) {
        const user = JSON.parse(userData);
        this.currentUser = user;
        return user;
      }
    } catch (error) {
      console.error('Error initializing auth service:', error);
    }
    return null;
  }

  // Login with email and password
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      // For demo purposes, we'll simulate JWT authentication
      // In a real app, you'd integrate with WordPress JWT Auth plugin
      const response = await this.simulateLogin(credentials);
      
      // Store user data and token
      await this.storeAuthData(response.user, response.token);
      this.currentUser = response.user;
      
      return response;
    } catch (error) {
      throw ErrorHandler.handleApiError(error);
    }
  }

  // Register new user
  async register(data: RegisterData): Promise<AuthResponse> {
    try {
      // Create customer in WooCommerce
      const customerData = {
        email: data.email,
        first_name: data.firstName,
        last_name: data.lastName,
        username: data.email,
        password: data.password,
        billing: {
          first_name: data.firstName,
          last_name: data.lastName,
          email: data.email,
          phone: data.phone || '',
          company: '',
          address_1: '',
          address_2: '',
          city: '',
          state: '',
          postcode: '',
          country: '',
        },
        shipping: {
          first_name: data.firstName,
          last_name: data.lastName,
          company: '',
          address_1: '',
          address_2: '',
          city: '',
          state: '',
          postcode: '',
          country: '',
        },
      };

      const customer = await wooCommerceAPI.createCustomer(customerData);
      
      // Create user object
      const user: User = {
        id: customer.id,
        email: customer.email,
        firstName: customer.first_name,
        lastName: customer.last_name,
        token: this.generateToken(),
      };

      const authResponse: AuthResponse = {
        user,
        token: user.token,
      };

      // Store auth data
      await this.storeAuthData(user, user.token);
      this.currentUser = user;

      return authResponse;
    } catch (error) {
      throw ErrorHandler.handleApiError(error);
    }
  }

  // Logout user
  async logout(): Promise<void> {
    try {
      // Clear stored data
      await Promise.all([
        AsyncStorage.multiRemove([
          STORAGE_KEYS.USER_DATA,
          STORAGE_KEYS.CART_DATA,
          STORAGE_KEYS.WISHLIST_DATA,
        ]),
        secureStorage.removeItem(STORAGE_KEYS.USER_TOKEN),
      ]);

      this.currentUser = null;
    } catch (error) {
      console.error('Error during logout:', error);
      throw error;
    }
  }

  // Get current user
  getCurrentUser(): User | null {
    return this.currentUser;
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return this.currentUser !== null;
  }

  // Update user profile
  async updateProfile(updates: Partial<User>): Promise<User> {
    if (!this.currentUser) {
      throw new Error('User not authenticated');
    }

    try {
      // Update in WooCommerce
      const customerUpdates = {
        first_name: updates.firstName,
        last_name: updates.lastName,
        email: updates.email,
      };

      const updatedCustomer = await wooCommerceAPI.updateCustomer(
        this.currentUser.id,
        customerUpdates
      );

      // Update local user data
      const updatedUser: User = {
        ...this.currentUser,
        ...updates,
        firstName: updatedCustomer.first_name,
        lastName: updatedCustomer.last_name,
        email: updatedCustomer.email,
      };

      await this.storeAuthData(updatedUser, this.currentUser.token);
      this.currentUser = updatedUser;

      return updatedUser;
    } catch (error) {
      throw ErrorHandler.handleApiError(error);
    }
  }

  // Change password
  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    if (!this.currentUser) {
      throw new Error('User not authenticated');
    }

    try {
      // In a real app, you'd verify current password and update
      // For now, we'll simulate the process
      await this.simulatePasswordChange(currentPassword, newPassword);
    } catch (error) {
      throw ErrorHandler.handleApiError(error);
    }
  }

  // Request password reset
  async requestPasswordReset(email: string): Promise<void> {
    try {
      // In a real app, this would send a reset email
      await this.simulatePasswordResetRequest(email);
    } catch (error) {
      throw ErrorHandler.handleApiError(error);
    }
  }

  // Confirm password reset
  async confirmPasswordReset(data: PasswordResetConfirm): Promise<void> {
    try {
      // In a real app, this would verify the code and reset password
      await this.simulatePasswordResetConfirm(data);
    } catch (error) {
      throw ErrorHandler.handleApiError(error);
    }
  }

  // Refresh authentication token
  async refreshToken(): Promise<string> {
    if (!this.currentUser) {
      throw new Error('User not authenticated');
    }

    try {
      // In a real app, you'd refresh the JWT token
      const newToken = this.generateToken();
      await secureStorage.setItem(STORAGE_KEYS.USER_TOKEN, newToken);
      return newToken;
    } catch (error) {
      throw ErrorHandler.handleApiError(error);
    }
  }

  // Private helper methods
  private async storeAuthData(user: User, token: string): Promise<void> {
    await Promise.all([
      AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(user)),
      secureStorage.setItem(STORAGE_KEYS.USER_TOKEN, token),
    ]);
  }

  private generateToken(): string {
    // Generate a simple token for demo purposes
    return `token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Simulation methods for demo purposes
  private async simulateLogin(credentials: LoginCredentials): Promise<AuthResponse> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Simple validation for demo
    if (credentials.email === '<EMAIL>' && credentials.password === 'password') {
      const user: User = {
        id: 1,
        email: credentials.email,
        firstName: 'Demo',
        lastName: 'User',
        token: this.generateToken(),
      };

      return {
        user,
        token: user.token,
      };
    }

    throw new Error('Invalid credentials');
  }

  private async simulatePasswordChange(currentPassword: string, newPassword: string): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (currentPassword !== 'password') {
      throw new Error('Current password is incorrect');
    }
    
    if (newPassword.length < 6) {
      throw new Error('New password must be at least 6 characters');
    }
  }

  private async simulatePasswordResetRequest(email: string): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 1000));
    console.log(`Password reset email sent to: ${email}`);
  }

  private async simulatePasswordResetConfirm(data: PasswordResetConfirm): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (data.code !== '123456') {
      throw new Error('Invalid reset code');
    }
    
    if (data.newPassword.length < 6) {
      throw new Error('Password must be at least 6 characters');
    }
  }
}

export default new AuthService();
