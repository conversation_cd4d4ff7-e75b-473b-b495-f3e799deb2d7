import React, { useRef } from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Animated,
  ViewStyle,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { COLORS, SPACING, BORDER_RADIUS, SHADOWS } from '../../constants';

interface PremiumCardProps {
  children: React.ReactNode;
  onPress?: () => void;
  style?: ViewStyle;
  variant?: 'default' | 'elevated' | 'gradient' | 'glass';
  padding?: 'none' | 'small' | 'medium' | 'large';
  borderRadius?: 'small' | 'medium' | 'large' | 'xl';
  shadow?: 'none' | 'light' | 'medium' | 'heavy';
  animated?: boolean;
  gradientColors?: string[];
  glassTint?: string;
}

const PremiumCard: React.FC<PremiumCardProps> = ({
  children,
  onPress,
  style,
  variant = 'default',
  padding = 'medium',
  borderRadius = 'medium',
  shadow = 'light',
  animated = true,
  gradientColors = [COLORS.primary, COLORS.secondary],
  glassTint = 'rgba(255, 255, 255, 0.1)',
}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    if (!animated || !onPress) return;
    
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 0.98,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 0.9,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handlePressOut = () => {
    if (!animated || !onPress) return;
    
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const getCardStyle = () => {
    const baseStyle = [styles.card];

    // Padding styles
    switch (padding) {
      case 'none':
        break;
      case 'small':
        baseStyle.push(styles.smallPadding);
        break;
      case 'medium':
        baseStyle.push(styles.mediumPadding);
        break;
      case 'large':
        baseStyle.push(styles.largePadding);
        break;
    }

    // Border radius styles
    switch (borderRadius) {
      case 'small':
        baseStyle.push(styles.smallRadius);
        break;
      case 'medium':
        baseStyle.push(styles.mediumRadius);
        break;
      case 'large':
        baseStyle.push(styles.largeRadius);
        break;
      case 'xl':
        baseStyle.push(styles.xlRadius);
        break;
    }

    // Shadow styles
    switch (shadow) {
      case 'none':
        break;
      case 'light':
        baseStyle.push(SHADOWS.light);
        break;
      case 'medium':
        baseStyle.push(SHADOWS.medium);
        break;
      case 'heavy':
        baseStyle.push(SHADOWS.heavy);
        break;
    }

    // Variant styles
    switch (variant) {
      case 'default':
        baseStyle.push(styles.defaultCard);
        break;
      case 'elevated':
        baseStyle.push(styles.elevatedCard);
        break;
      case 'glass':
        baseStyle.push(styles.glassCard);
        break;
    }

    return baseStyle;
  };

  const renderCard = () => {
    const cardContent = (
      <Animated.View
        style={[
          getCardStyle(),
          style,
          animated && {
            transform: [{ scale: scaleAnim }],
            opacity: opacityAnim,
          },
        ]}
      >
        {children}
      </Animated.View>
    );

    if (variant === 'gradient') {
      return (
        <LinearGradient
          colors={gradientColors}
          style={[
            getCardStyle(),
            style,
            animated && {
              transform: [{ scale: scaleAnim }],
              opacity: opacityAnim,
            },
          ]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          {children}
        </LinearGradient>
      );
    }

    if (variant === 'glass') {
      return (
        <Animated.View
          style={[
            getCardStyle(),
            { backgroundColor: glassTint },
            style,
            animated && {
              transform: [{ scale: scaleAnim }],
              opacity: opacityAnim,
            },
          ]}
        >
          <View style={styles.glassOverlay} />
          {children}
        </Animated.View>
      );
    }

    return cardContent;
  };

  if (onPress) {
    return (
      <TouchableOpacity
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}
      >
        {renderCard()}
      </TouchableOpacity>
    );
  }

  return renderCard();
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: COLORS.white,
    overflow: 'hidden',
  },

  // Padding styles
  smallPadding: {
    padding: SPACING.sm,
  },
  mediumPadding: {
    padding: SPACING.md,
  },
  largePadding: {
    padding: SPACING.lg,
  },

  // Border radius styles
  smallRadius: {
    borderRadius: BORDER_RADIUS.sm,
  },
  mediumRadius: {
    borderRadius: BORDER_RADIUS.md,
  },
  largeRadius: {
    borderRadius: BORDER_RADIUS.lg,
  },
  xlRadius: {
    borderRadius: BORDER_RADIUS.xl,
  },

  // Variant styles
  defaultCard: {
    backgroundColor: COLORS.white,
  },
  elevatedCard: {
    backgroundColor: COLORS.white,
    elevation: 8,
  },
  glassCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    backdropFilter: 'blur(10px)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  glassOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '50%',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
});

export default PremiumCard;
