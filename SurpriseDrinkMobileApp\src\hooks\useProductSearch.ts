import { useState, useEffect, useCallback } from 'react';
import { WooCommerceProduct } from '../types';
import { ProductFilters } from '../components/product/ProductFilters';
import wooCommerceAPI from '../services/woocommerce';
import { <PERSON>rrorHandler } from '../utils/errorHandler';

interface UseProductSearchOptions {
  initialQuery?: string;
  initialFilters?: ProductFilters;
  debounceMs?: number;
  autoSearch?: boolean;
}

interface UseProductSearchReturn {
  // State
  products: WooCommerceProduct[];
  loading: boolean;
  error: string | null;
  hasSearched: boolean;
  
  // Search functionality
  search: (query?: string, filters?: ProductFilters) => Promise<void>;
  clearSearch: () => void;
  
  // Pagination
  loadMore: () => Promise<void>;
  hasMore: boolean;
  
  // Refresh
  refresh: () => Promise<void>;
  refreshing: boolean;
}

export const useProductSearch = ({
  initialQuery = '',
  initialFilters = {},
  debounceMs = 300,
  autoSearch = false,
}: UseProductSearchOptions = {}): UseProductSearchReturn => {
  const [products, setProducts] = useState<WooCommerceProduct[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasSearched, setHasSearched] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [currentQuery, setCurrentQuery] = useState(initialQuery);
  const [currentFilters, setCurrentFilters] = useState<ProductFilters>(initialFilters);

  // Debounced search function
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);

  const performSearch = useCallback(async (
    query: string = currentQuery,
    filters: ProductFilters = currentFilters,
    page: number = 1,
    append: boolean = false
  ) => {
    try {
      if (!append) {
        setLoading(true);
      }
      setError(null);

      const searchParams = {
        search: query.trim() || undefined,
        category: filters.category,
        featured: filters.featured,
        on_sale: filters.onSale,
        orderby: filters.sortBy || 'date',
        order: filters.sortOrder || 'desc',
        page,
        per_page: 20,
        // Price range filtering (if supported by API)
        min_price: filters.priceRange?.min,
        max_price: filters.priceRange?.max,
        // Stock status
        stock_status: filters.inStock ? 'instock' : undefined,
      };

      const results = await wooCommerceAPI.getProducts(searchParams);
      
      if (append) {
        setProducts(prev => [...prev, ...results]);
      } else {
        setProducts(results);
      }
      
      setHasMore(results.length === 20); // Assuming 20 is the page size
      setCurrentPage(page);
      setHasSearched(true);
    } catch (err) {
      const apiError = ErrorHandler.handleApiError(err);
      setError(apiError.message);
      console.error('Search error:', err);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [currentQuery, currentFilters]);

  const search = useCallback(async (
    query: string = currentQuery,
    filters: ProductFilters = currentFilters
  ) => {
    setCurrentQuery(query);
    setCurrentFilters(filters);
    setCurrentPage(1);
    await performSearch(query, filters, 1, false);
  }, [performSearch, currentQuery, currentFilters]);

  const debouncedSearch = useCallback((
    query: string,
    filters: ProductFilters = currentFilters
  ) => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    const timeout = setTimeout(() => {
      search(query, filters);
    }, debounceMs);

    setSearchTimeout(timeout);
  }, [search, searchTimeout, debounceMs, currentFilters]);

  const loadMore = useCallback(async () => {
    if (!hasMore || loading) return;
    
    const nextPage = currentPage + 1;
    await performSearch(currentQuery, currentFilters, nextPage, true);
  }, [hasMore, loading, currentPage, performSearch, currentQuery, currentFilters]);

  const refresh = useCallback(async () => {
    setRefreshing(true);
    setCurrentPage(1);
    await performSearch(currentQuery, currentFilters, 1, false);
  }, [performSearch, currentQuery, currentFilters]);

  const clearSearch = useCallback(() => {
    setProducts([]);
    setCurrentQuery('');
    setCurrentFilters({});
    setHasSearched(false);
    setError(null);
    setCurrentPage(1);
    setHasMore(true);
    
    if (searchTimeout) {
      clearTimeout(searchTimeout);
      setSearchTimeout(null);
    }
  }, [searchTimeout]);

  // Auto search on mount if enabled
  useEffect(() => {
    if (autoSearch && (initialQuery || Object.keys(initialFilters).length > 0)) {
      search(initialQuery, initialFilters);
    }
  }, [autoSearch, initialQuery, initialFilters, search]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTimeout]);

  return {
    // State
    products,
    loading,
    error,
    hasSearched,
    
    // Search functionality
    search,
    clearSearch,
    
    // Pagination
    loadMore,
    hasMore,
    
    // Refresh
    refresh,
    refreshing,
  };
};

// Hook for category-specific product loading
export const useCategoryProducts = (categoryId?: number) => {
  return useProductSearch({
    initialFilters: categoryId ? { category: categoryId } : {},
    autoSearch: !!categoryId,
  });
};

// Hook for featured products
export const useFeaturedProducts = () => {
  return useProductSearch({
    initialFilters: { featured: true },
    autoSearch: true,
  });
};

// Hook for sale products
export const useSaleProducts = () => {
  return useProductSearch({
    initialFilters: { onSale: true },
    autoSearch: true,
  });
};

// Hook for latest products
export const useLatestProducts = () => {
  return useProductSearch({
    initialFilters: { sortBy: 'date', sortOrder: 'desc' },
    autoSearch: true,
  });
};
