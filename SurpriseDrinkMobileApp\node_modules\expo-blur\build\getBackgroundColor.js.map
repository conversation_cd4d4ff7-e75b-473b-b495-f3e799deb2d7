{"version": 3, "file": "getBackgroundColor.js", "sourceRoot": "", "sources": ["../src/getBackgroundColor.ts"], "names": [], "mappings": "AAEA,MAAM,CAAC,OAAO,UAAU,kBAAkB,CAAC,SAAiB,EAAE,IAAc;IAC1E,MAAM,OAAO,GAAG,SAAS,GAAG,GAAG,CAAC;IAChC,QAAQ,IAAI,EAAE,CAAC;QACb,+EAA+E;QAC/E,+EAA+E;QAC/E,KAAK,MAAM,CAAC;QACZ,KAAK,oBAAoB;YACvB,OAAO,iBAAiB,OAAO,GAAG,IAAI,GAAG,CAAC;QAC5C,KAAK,OAAO,CAAC;QACb,KAAK,YAAY,CAAC;QAClB,KAAK,qBAAqB,CAAC;QAC3B,KAAK,8BAA8B,CAAC;QACpC,KAAK,0BAA0B;YAC7B,OAAO,oBAAoB,OAAO,GAAG,IAAI,GAAG,CAAC;QAE/C,KAAK,SAAS,CAAC;QACf,KAAK,WAAW,CAAC;QACjB,KAAK,gBAAgB;YACnB,OAAO,oBAAoB,OAAO,GAAG,GAAG,GAAG,CAAC;QAC9C,KAAK,SAAS;YACZ,OAAO,oBAAoB,OAAO,GAAG,IAAI,GAAG,CAAC;QAC/C,KAAK,oBAAoB;YACvB,OAAO,oBAAoB,OAAO,GAAG,IAAI,GAAG,CAAC;QAC/C,KAAK,sBAAsB;YACzB,OAAO,oBAAoB,OAAO,GAAG,IAAI,GAAG,CAAC;QAC/C,KAAK,2BAA2B;YAC9B,OAAO,oBAAoB,OAAO,GAAG,IAAI,GAAG,CAAC;QAC/C,KAAK,yBAAyB;YAC5B,OAAO,oBAAoB,OAAO,GAAG,IAAI,GAAG,CAAC;QAC/C,KAAK,qBAAqB;YACxB,OAAO,oBAAoB,OAAO,GAAG,IAAI,GAAG,CAAC;QAC/C,KAAK,yBAAyB;YAC5B,OAAO,iBAAiB,OAAO,GAAG,GAAG,GAAG,CAAC;QAC3C,KAAK,wBAAwB;YAC3B,OAAO,iBAAiB,OAAO,GAAG,GAAG,GAAG,CAAC;QAC3C,KAAK,6BAA6B;YAChC,OAAO,iBAAiB,OAAO,GAAG,IAAI,GAAG,CAAC;QAC5C,KAAK,0BAA0B;YAC7B,OAAO,cAAc,OAAO,GAAG,IAAI,GAAG,CAAC;QACzC,KAAK,yBAAyB;YAC5B,OAAO,oBAAoB,OAAO,GAAG,IAAI,GAAG,CAAC;IACjD,CAAC;AACH,CAAC", "sourcesContent": ["import { BlurTint } from './BlurView.types';\n\nexport default function getBackgroundColor(intensity: number, tint: BlurTint): string {\n  const opacity = intensity / 100;\n  switch (tint) {\n    // From Apple iOS 14 Sketch Kit - https://developer.apple.com/design/resources/\n    // From Apple iOS 14 Sketch Kit - https://developer.apple.com/design/resources/\n    case 'dark':\n    case 'systemMaterialDark':\n      return `rgba(25,25,25,${opacity * 0.78})`;\n    case 'light':\n    case 'extraLight':\n    case 'systemMaterialLight':\n    case 'systemUltraThinMaterialLight':\n    case 'systemThickMaterialLight':\n      return `rgba(249,249,249,${opacity * 0.78})`;\n\n    case 'default':\n    case 'prominent':\n    case 'systemMaterial':\n      return `rgba(255,255,255,${opacity * 0.3})`;\n    case 'regular':\n      return `rgba(179,179,179,${opacity * 0.82})`;\n    case 'systemThinMaterial':\n      return `rgba(199,199,199,${opacity * 0.97})`;\n    case 'systemChromeMaterial':\n      return `rgba(255,255,255,${opacity * 0.75})`;\n    case 'systemChromeMaterialLight':\n      return `rgba(255,255,255,${opacity * 0.97})`;\n    case 'systemUltraThinMaterial':\n      return `rgba(191,191,191,${opacity * 0.44})`;\n    case 'systemThickMaterial':\n      return `rgba(191,191,191,${opacity * 0.44})`;\n    case 'systemThickMaterialDark':\n      return `rgba(37,37,37,${opacity * 0.9})`;\n    case 'systemThinMaterialDark':\n      return `rgba(37,37,37,${opacity * 0.7})`;\n    case 'systemUltraThinMaterialDark':\n      return `rgba(37,37,37,${opacity * 0.55})`;\n    case 'systemChromeMaterialDark':\n      return `rgba(0,0,0,${opacity * 0.75})`;\n    case 'systemThinMaterialLight':\n      return `rgba(199,199,199,${opacity * 0.78})`;\n  }\n}\n"]}