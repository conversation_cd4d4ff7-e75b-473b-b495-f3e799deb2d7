import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../../constants';
import PremiumCard from '../common/PremiumCard';

interface CartSummaryProps {
  subtotal: number;
  shipping: number;
  tax: number;
  total: number;
  itemCount: number;
  showShippingNote?: boolean;
  style?: any;
}

const CartSummary: React.FC<CartSummaryProps> = ({
  subtotal,
  shipping,
  tax,
  total,
  itemCount,
  showShippingNote = true,
  style,
}) => {
  const freeShippingThreshold = 50;
  const remainingForFreeShipping = freeShippingThreshold - subtotal;

  return (
    <PremiumCard style={[styles.container, style]} padding="large" shadow="medium">
      <Text style={styles.title}>Order Summary</Text>
      
      <View style={styles.row}>
        <Text style={styles.label}>
          Items ({itemCount} {itemCount === 1 ? 'item' : 'items'})
        </Text>
        <Text style={styles.value}>${subtotal.toFixed(2)}</Text>
      </View>
      
      <View style={styles.row}>
        <Text style={styles.label}>Shipping</Text>
        <Text style={[styles.value, shipping === 0 && styles.freeShipping]}>
          {shipping === 0 ? 'FREE' : `$${shipping.toFixed(2)}`}
        </Text>
      </View>
      
      <View style={styles.row}>
        <Text style={styles.label}>Tax</Text>
        <Text style={styles.value}>${tax.toFixed(2)}</Text>
      </View>
      
      <View style={[styles.row, styles.totalRow]}>
        <Text style={styles.totalLabel}>Total</Text>
        <Text style={styles.totalValue}>${total.toFixed(2)}</Text>
      </View>
      
      {showShippingNote && remainingForFreeShipping > 0 && (
        <View style={styles.shippingNoteContainer}>
          <Text style={styles.shippingNote}>
            Add ${remainingForFreeShipping.toFixed(2)} more for free shipping!
          </Text>
        </View>
      )}
      
      {shipping === 0 && subtotal >= freeShippingThreshold && (
        <View style={styles.freeShippingBadge}>
          <Text style={styles.freeShippingText}>🎉 You qualify for free shipping!</Text>
        </View>
      )}
    </PremiumCard>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: SPACING.md,
  },
  title: {
    fontSize: FONTS.lg,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: SPACING.lg,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  label: {
    fontSize: FONTS.md,
    color: COLORS.textSecondary,
  },
  value: {
    fontSize: FONTS.md,
    fontWeight: '500',
    color: COLORS.textPrimary,
  },
  freeShipping: {
    color: COLORS.success,
    fontWeight: '600',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    paddingTop: SPACING.md,
    marginTop: SPACING.md,
    marginBottom: 0,
  },
  totalLabel: {
    fontSize: FONTS.lg,
    fontWeight: '600',
    color: COLORS.textPrimary,
  },
  totalValue: {
    fontSize: FONTS.lg,
    fontWeight: '700',
    color: COLORS.primary,
  },
  shippingNoteContainer: {
    marginTop: SPACING.md,
    padding: SPACING.md,
    backgroundColor: COLORS.info + '10',
    borderRadius: BORDER_RADIUS.sm,
    borderLeftWidth: 3,
    borderLeftColor: COLORS.info,
  },
  shippingNote: {
    fontSize: FONTS.sm,
    color: COLORS.info,
    fontWeight: '500',
    textAlign: 'center',
  },
  freeShippingBadge: {
    marginTop: SPACING.md,
    padding: SPACING.md,
    backgroundColor: COLORS.success + '10',
    borderRadius: BORDER_RADIUS.sm,
    borderLeftWidth: 3,
    borderLeftColor: COLORS.success,
  },
  freeShippingText: {
    fontSize: FONTS.sm,
    color: COLORS.success,
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default CartSummary;
