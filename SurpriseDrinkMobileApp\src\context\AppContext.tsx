import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { WooCommerceProduct, CartItem, User, AppState } from '../types';
import { STORAGE_KEYS } from '../constants';

// Action types
type AppAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'ADD_TO_CART'; payload: { product: WooCommerceProduct; quantity: number } }
  | { type: 'REMOVE_FROM_CART'; payload: number }
  | { type: 'UPDATE_CART_QUANTITY'; payload: { productId: number; quantity: number } }
  | { type: 'CLEAR_CART' }
  | { type: 'SET_CART'; payload: CartItem[] }
  | { type: 'ADD_TO_WISHLIST'; payload: WooCommerceProduct }
  | { type: 'REMOVE_FROM_WISHLIST'; payload: number }
  | { type: 'SET_WISHLIST'; payload: WooCommerceProduct[] }
  | { type: 'CLEAR_WISHLIST' };

// Initial state
const initialState: AppState = {
  user: null,
  cart: [],
  wishlist: [],
  isLoading: false,
  error: null,
};

// Reducer
const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };

    case 'SET_ERROR':
      return { ...state, error: action.payload };

    case 'SET_USER':
      return { ...state, user: action.payload };

    case 'ADD_TO_CART': {
      const existingItemIndex = state.cart.findIndex(
        item => item.product.id === action.payload.product.id
      );

      if (existingItemIndex >= 0) {
        const updatedCart = [...state.cart];
        updatedCart[existingItemIndex].quantity += action.payload.quantity;
        return { ...state, cart: updatedCart };
      } else {
        return {
          ...state,
          cart: [...state.cart, { product: action.payload.product, quantity: action.payload.quantity }],
        };
      }
    }

    case 'REMOVE_FROM_CART':
      return {
        ...state,
        cart: state.cart.filter(item => item.product.id !== action.payload),
      };

    case 'UPDATE_CART_QUANTITY': {
      const updatedCart = state.cart.map(item =>
        item.product.id === action.payload.productId
          ? { ...item, quantity: action.payload.quantity }
          : item
      );
      return { ...state, cart: updatedCart };
    }

    case 'CLEAR_CART':
      return { ...state, cart: [] };

    case 'SET_CART':
      return { ...state, cart: action.payload };

    case 'ADD_TO_WISHLIST': {
      const isAlreadyInWishlist = state.wishlist.some(item => item.id === action.payload.id);
      if (isAlreadyInWishlist) {
        return state;
      }
      return { ...state, wishlist: [...state.wishlist, action.payload] };
    }

    case 'REMOVE_FROM_WISHLIST':
      return {
        ...state,
        wishlist: state.wishlist.filter(item => item.id !== action.payload),
      };

    case 'SET_WISHLIST':
      return { ...state, wishlist: action.payload };

    case 'CLEAR_WISHLIST':
      return { ...state, wishlist: [] };

    default:
      return state;
  }
};

// Context
interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  // Helper functions
  addToCart: (product: WooCommerceProduct, quantity?: number) => void;
  removeFromCart: (productId: number) => void;
  updateCartQuantity: (productId: number, quantity: number) => void;
  clearCart: () => void;
  addToWishlist: (product: WooCommerceProduct) => void;
  removeFromWishlist: (productId: number) => void;
  clearWishlist: () => void;
  isInWishlist: (productId: number) => boolean;
  getCartTotal: () => number;
  getCartItemsCount: () => number;
  setUser: (user: User | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

// Provider component
interface AppProviderProps {
  children: ReactNode;
}

export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Load data from AsyncStorage on app start
  useEffect(() => {
    loadStoredData();
  }, []);

  // Save cart to AsyncStorage whenever it changes
  useEffect(() => {
    saveCartToStorage();
  }, [state.cart]);

  // Save wishlist to AsyncStorage whenever it changes
  useEffect(() => {
    saveWishlistToStorage();
  }, [state.wishlist]);

  // Save user to AsyncStorage whenever it changes
  useEffect(() => {
    saveUserToStorage();
  }, [state.user]);

  const loadStoredData = async () => {
    try {
      // Load user data
      const userData = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);
      if (userData) {
        const user = JSON.parse(userData);
        dispatch({ type: 'SET_USER', payload: user });
      }

      // Load cart data
      const cartData = await AsyncStorage.getItem(STORAGE_KEYS.CART_DATA);
      if (cartData) {
        const cart = JSON.parse(cartData);
        dispatch({ type: 'SET_CART', payload: cart });
      }

      // Load wishlist data
      const wishlistData = await AsyncStorage.getItem(STORAGE_KEYS.WISHLIST_DATA);
      if (wishlistData) {
        const wishlist = JSON.parse(wishlistData);
        dispatch({ type: 'SET_WISHLIST', payload: wishlist });
      }
    } catch (error) {
      console.error('Error loading stored data:', error);
    }
  };

  const saveCartToStorage = async () => {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.CART_DATA, JSON.stringify(state.cart));
    } catch (error) {
      console.error('Error saving cart to storage:', error);
    }
  };

  const saveWishlistToStorage = async () => {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.WISHLIST_DATA, JSON.stringify(state.wishlist));
    } catch (error) {
      console.error('Error saving wishlist to storage:', error);
    }
  };

  const saveUserToStorage = async () => {
    try {
      if (state.user) {
        await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(state.user));
      } else {
        await AsyncStorage.removeItem(STORAGE_KEYS.USER_DATA);
      }
    } catch (error) {
      console.error('Error saving user to storage:', error);
    }
  };

  // Helper functions
  const addToCart = (product: WooCommerceProduct, quantity: number = 1) => {
    dispatch({ type: 'ADD_TO_CART', payload: { product, quantity } });
  };

  const removeFromCart = (productId: number) => {
    dispatch({ type: 'REMOVE_FROM_CART', payload: productId });
  };

  const updateCartQuantity = (productId: number, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(productId);
    } else {
      dispatch({ type: 'UPDATE_CART_QUANTITY', payload: { productId, quantity } });
    }
  };

  const clearCart = () => {
    dispatch({ type: 'CLEAR_CART' });
  };

  const addToWishlist = (product: WooCommerceProduct) => {
    dispatch({ type: 'ADD_TO_WISHLIST', payload: product });
  };

  const removeFromWishlist = (productId: number) => {
    dispatch({ type: 'REMOVE_FROM_WISHLIST', payload: productId });
  };

  const clearWishlist = () => {
    dispatch({ type: 'CLEAR_WISHLIST' });
  };

  const isInWishlist = (productId: number): boolean => {
    return state.wishlist.some(item => item.id === productId);
  };

  const getCartTotal = (): number => {
    return state.cart.reduce((total, item) => {
      const price = parseFloat(item.product.price) || 0;
      return total + (price * item.quantity);
    }, 0);
  };

  const getCartItemsCount = (): number => {
    return state.cart.reduce((count, item) => count + item.quantity, 0);
  };

  const setUser = (user: User | null) => {
    dispatch({ type: 'SET_USER', payload: user });
  };

  const setLoading = (loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: loading });
  };

  const setError = (error: string | null) => {
    dispatch({ type: 'SET_ERROR', payload: error });
  };

  const contextValue: AppContextType = {
    state,
    dispatch,
    addToCart,
    removeFromCart,
    updateCartQuantity,
    clearCart,
    addToWishlist,
    removeFromWishlist,
    clearWishlist,
    isInWishlist,
    getCartTotal,
    getCartItemsCount,
    setUser,
    setLoading,
    setError,
  };

  return <AppContext.Provider value={contextValue}>{children}</AppContext.Provider>;
};

// Custom hook to use the context
export const useApp = (): AppContextType => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};
