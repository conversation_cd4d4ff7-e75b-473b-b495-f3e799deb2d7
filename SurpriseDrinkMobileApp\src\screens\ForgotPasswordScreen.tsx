import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';

import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../constants';
import { useAuth } from '../context/AuthContext';
import Button from '../components/common/Button';
import Input from '../components/common/Input';
import { useFormValidation, Validator } from '../utils/validation';

const ForgotPasswordScreen: React.FC = () => {
  const navigation = useNavigation();
  const { requestPasswordReset, state, clearError } = useAuth();
  
  const {
    data,
    setValue,
    setFieldTouched,
    validateAll,
    getFieldError,
  } = useFormValidation(
    { email: '' },
    {
      email: [Validator.required(), Validator.email()],
    }
  );

  const handleResetRequest = async () => {
    if (!validateAll()) {
      Alert.alert('Error', 'Please enter a valid email address');
      return;
    }

    try {
      clearError();
      await requestPasswordReset(data.email);
      
      Alert.alert(
        'Reset Email Sent',
        'If an account with this email exists, you will receive password reset instructions.',
        [
          { text: 'OK', onPress: () => navigation.goBack() }
        ]
      );
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to send reset email.');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.content}>
          <Text style={styles.title}>Forgot Password?</Text>
          <Text style={styles.subtitle}>
            Enter your email address and we'll send you instructions to reset your password.
          </Text>

          <View style={styles.form}>
            <Input
              label="Email Address"
              value={data.email}
              onChangeText={(text) => setValue('email', text)}
              onBlur={() => setFieldTouched('email')}
              placeholder="Enter your email"
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
              leftIcon="mail-outline"
              error={getFieldError('email')}
              required
            />

            <Button
              title="Send Reset Instructions"
              onPress={handleResetRequest}
              fullWidth
              size="large"
              style={styles.resetButton}
              loading={state.isLoading}
              disabled={state.isLoading}
            />

            <Button
              title="Back to Login"
              onPress={() => navigation.goBack()}
              variant="ghost"
              fullWidth
              style={styles.backButton}
            />
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
    justifyContent: 'center',
  },
  title: {
    fontSize: FONTS.xxxl,
    fontWeight: '700',
    color: COLORS.textPrimary,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  subtitle: {
    fontSize: FONTS.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: FONTS.lineHeight.md,
    marginBottom: SPACING.xl,
  },
  form: {
    width: '100%',
  },
  resetButton: {
    marginBottom: SPACING.lg,
  },
  backButton: {
    marginTop: SPACING.md,
  },
});

export default ForgotPasswordScreen;
