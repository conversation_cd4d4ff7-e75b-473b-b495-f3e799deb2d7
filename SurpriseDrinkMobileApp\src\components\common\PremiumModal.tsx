import React, { useRef, useEffect } from 'react';
import {
  Modal,
  View,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
  TouchableWithoutFeedback,
  StatusBar,
  ViewStyle,
} from 'react-native';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, SPACING, BORDER_RADIUS, SHADOWS } from '../../constants';

const { width, height } = Dimensions.get('window');

interface PremiumModalProps {
  visible: boolean;
  onClose: () => void;
  children: React.ReactNode;
  title?: string;
  size?: 'small' | 'medium' | 'large' | 'fullscreen';
  position?: 'center' | 'bottom' | 'top';
  animationType?: 'slide' | 'fade' | 'scale';
  backdrop?: 'blur' | 'dark' | 'light';
  closeOnBackdrop?: boolean;
  showCloseButton?: boolean;
  style?: ViewStyle;
  contentStyle?: ViewStyle;
}

const PremiumModal: React.FC<PremiumModalProps> = ({
  visible,
  onClose,
  children,
  title,
  size = 'medium',
  position = 'center',
  animationType = 'scale',
  backdrop = 'blur',
  closeOnBackdrop = true,
  showCloseButton = true,
  style,
  contentStyle,
}) => {
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(height)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const backdropAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (visible) {
      // Show animation
      Animated.parallel([
        Animated.timing(backdropAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        getShowAnimation(),
      ]).start();
    } else {
      // Hide animation
      Animated.parallel([
        Animated.timing(backdropAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        getHideAnimation(),
      ]).start();
    }
  }, [visible]);

  const getShowAnimation = () => {
    switch (animationType) {
      case 'slide':
        return Animated.spring(slideAnim, {
          toValue: 0,
          useNativeDriver: true,
        });
      case 'fade':
        return Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        });
      case 'scale':
      default:
        return Animated.spring(scaleAnim, {
          toValue: 1,
          useNativeDriver: true,
        });
    }
  };

  const getHideAnimation = () => {
    switch (animationType) {
      case 'slide':
        return Animated.timing(slideAnim, {
          toValue: height,
          duration: 200,
          useNativeDriver: true,
        });
      case 'fade':
        return Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        });
      case 'scale':
      default:
        return Animated.timing(scaleAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        });
    }
  };

  const getModalSize = () => {
    switch (size) {
      case 'small':
        return { width: width * 0.8, maxHeight: height * 0.4 };
      case 'medium':
        return { width: width * 0.9, maxHeight: height * 0.6 };
      case 'large':
        return { width: width * 0.95, maxHeight: height * 0.8 };
      case 'fullscreen':
        return { width: width, height: height };
      default:
        return { width: width * 0.9, maxHeight: height * 0.6 };
    }
  };

  const getPositionStyle = () => {
    const modalSize = getModalSize();
    
    switch (position) {
      case 'top':
        return {
          justifyContent: 'flex-start',
          paddingTop: StatusBar.currentHeight || 44,
        };
      case 'bottom':
        return {
          justifyContent: 'flex-end',
          paddingBottom: 34, // Safe area bottom
        };
      case 'center':
      default:
        return {
          justifyContent: 'center',
          alignItems: 'center',
        };
    }
  };

  const getAnimationStyle = () => {
    switch (animationType) {
      case 'slide':
        return {
          transform: [{ translateY: slideAnim }],
        };
      case 'fade':
        return {
          opacity: fadeAnim,
        };
      case 'scale':
      default:
        return {
          transform: [{ scale: scaleAnim }],
          opacity: scaleAnim,
        };
    }
  };

  const renderBackdrop = () => {
    if (backdrop === 'blur') {
      return (
        <Animated.View style={[styles.backdrop, { opacity: backdropAnim }]}>
          <BlurView intensity={20} style={StyleSheet.absoluteFill} />
        </Animated.View>
      );
    }

    const backdropColor = backdrop === 'dark' 
      ? 'rgba(0, 0, 0, 0.5)' 
      : 'rgba(255, 255, 255, 0.9)';

    return (
      <Animated.View
        style={[
          styles.backdrop,
          { backgroundColor: backdropColor, opacity: backdropAnim },
        ]}
      />
    );
  };

  const handleBackdropPress = () => {
    if (closeOnBackdrop) {
      onClose();
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      statusBarTranslucent
    >
      <View style={styles.container}>
        {renderBackdrop()}
        
        <TouchableWithoutFeedback onPress={handleBackdropPress}>
          <View style={[styles.modalContainer, getPositionStyle()]}>
            <TouchableWithoutFeedback>
              <Animated.View
                style={[
                  styles.modal,
                  getModalSize(),
                  getAnimationStyle(),
                  style,
                ]}
              >
                {showCloseButton && (
                  <TouchableOpacity
                    style={styles.closeButton}
                    onPress={onClose}
                  >
                    <Ionicons name="close" size={24} color={COLORS.textPrimary} />
                  </TouchableOpacity>
                )}
                
                <View style={[styles.content, contentStyle]}>
                  {children}
                </View>
              </Animated.View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
  },
  modalContainer: {
    flex: 1,
    padding: SPACING.lg,
  },
  modal: {
    backgroundColor: COLORS.white,
    borderRadius: BORDER_RADIUS.lg,
    ...SHADOWS.heavy,
    overflow: 'hidden',
  },
  closeButton: {
    position: 'absolute',
    top: SPACING.md,
    right: SPACING.md,
    zIndex: 1,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
});

export default PremiumModal;
