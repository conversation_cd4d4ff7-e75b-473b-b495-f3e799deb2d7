{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/drawer": "^7.5.3", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "axios": "^1.10.0", "expo": "~53.0.17", "expo-blur": "^14.1.5", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.5", "expo-secure-store": "^14.2.3", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-elements": "^3.4.3", "react-native-gesture-handler": "^2.27.1", "react-native-modal": "^14.0.0-rc.1", "react-native-paper": "^5.14.5", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.5.1", "react-native-screens": "^4.11.1", "react-native-snap-carousel": "^3.9.1", "react-native-super-grid": "^6.0.1", "react-native-toast-message": "^2.3.3", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}