import * as React from 'react';
import GenericTouchable from './GenericTouchable';
import type { GenericTouchableProps } from './GenericTouchableProps';
/**
 * @deprecated TouchableWithoutFeedback will be removed in the future version of Gesture Handler. Use Pressable instead.
 */
export type TouchableWithoutFeedbackProps = GenericTouchableProps;
/**
 * @deprecated TouchableWithoutFeedback will be removed in the future version of Gesture Handler. Use Pressable instead.
 */
declare const TouchableWithoutFeedback: React.ForwardRefExoticComponent<GenericTouchableProps & {
    children?: React.ReactNode | undefined;
} & React.RefAttributes<GenericTouchable>>;
export default TouchableWithoutFeedback;
