import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Switch,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import type { NavigationProp } from '@react-navigation/native';

import { COLORS, FONTS, SPACING, BORDER_RADIUS, SHADOWS } from '../constants';
import { RootStackParamList } from '../types';
import { useAuth } from '../context/AuthContext';
import { useApp } from '../context/AppContext';
import PremiumCard from '../components/common/PremiumCard';
import AnimatedButton from '../components/common/AnimatedButton';
import CachedImage from '../components/common/CachedImage';

const { width } = Dimensions.get('window');

const EnhancedProfileScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { state: authState, logout } = useAuth();
  const { state: appState } = useApp();
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [emailUpdates, setEmailUpdates] = useState(true);

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: logout,
        },
      ]
    );
  };

  const handleLogin = () => {
    navigation.navigate('Login');
  };

  const renderUserHeader = () => {
    if (!authState.isAuthenticated) {
      return (
        <PremiumCard style={styles.headerCard} padding="large" shadow="medium">
          <View style={styles.guestHeader}>
            <View style={styles.guestAvatar}>
              <Ionicons name="person-outline" size={40} color={COLORS.gray} />
            </View>
            <View style={styles.guestInfo}>
              <Text style={styles.guestTitle}>Welcome to Surprise Drink</Text>
              <Text style={styles.guestSubtitle}>
                Login to access your profile and orders
              </Text>
            </View>
          </View>
          <AnimatedButton
            title="Login"
            onPress={handleLogin}
            variant="primary"
            size="medium"
            style={styles.loginButton}
            icon={<Ionicons name="log-in-outline" size={20} color={COLORS.white} />}
          />
        </PremiumCard>
      );
    }

    return (
      <PremiumCard style={styles.headerCard} padding="large" shadow="medium">
        <View style={styles.userHeader}>
          <View style={styles.avatarContainer}>
            <CachedImage
              uri={authState.user?.avatar || ''}
              style={styles.avatar}
              placeholder="https://via.placeholder.com/80x80/E91E63/FFFFFF?text=User"
              fallbackIcon="person"
            />
            <TouchableOpacity style={styles.editAvatarButton}>
              <Ionicons name="camera" size={16} color={COLORS.white} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.userInfo}>
            <Text style={styles.userName}>
              {authState.user?.display_name || authState.user?.username || 'User'}
            </Text>
            <Text style={styles.userEmail}>
              {authState.user?.email || '<EMAIL>'}
            </Text>
            <View style={styles.membershipBadge}>
              <Ionicons name="star" size={12} color={COLORS.warning} />
              <Text style={styles.membershipText}>Premium Member</Text>
            </View>
          </View>
        </View>
      </PremiumCard>
    );
  };

  const renderQuickStats = () => {
    if (!authState.isAuthenticated) return null;

    const cartItemCount = appState.cart.length;
    const wishlistCount = appState.wishlist.length;

    return (
      <View style={styles.statsContainer}>
        <PremiumCard style={styles.statCard} padding="medium" shadow="light">
          <View style={styles.statContent}>
            <Ionicons name="bag-outline" size={24} color={COLORS.primary} />
            <Text style={styles.statNumber}>{cartItemCount}</Text>
            <Text style={styles.statLabel}>Cart Items</Text>
          </View>
        </PremiumCard>
        
        <PremiumCard style={styles.statCard} padding="medium" shadow="light">
          <View style={styles.statContent}>
            <Ionicons name="heart-outline" size={24} color={COLORS.error} />
            <Text style={styles.statNumber}>{wishlistCount}</Text>
            <Text style={styles.statLabel}>Wishlist</Text>
          </View>
        </PremiumCard>
        
        <PremiumCard style={styles.statCard} padding="medium" shadow="light">
          <View style={styles.statContent}>
            <Ionicons name="receipt-outline" size={24} color={COLORS.success} />
            <Text style={styles.statNumber}>12</Text>
            <Text style={styles.statLabel}>Orders</Text>
          </View>
        </PremiumCard>
      </View>
    );
  };

  const renderMenuSection = (title: string, items: any[]) => (
    <View style={styles.menuSection}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <PremiumCard style={styles.menuCard} padding="none" shadow="light">
        {items.map((item, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.menuItem,
              index === items.length - 1 && styles.lastMenuItem,
            ]}
            onPress={item.onPress}
            disabled={item.disabled}
          >
            <View style={styles.menuItemLeft}>
              <View style={[styles.menuIcon, { backgroundColor: item.iconBg }]}>
                <Ionicons name={item.icon} size={20} color={item.iconColor} />
              </View>
              <View style={styles.menuItemText}>
                <Text style={styles.menuItemTitle}>{item.title}</Text>
                {item.subtitle && (
                  <Text style={styles.menuItemSubtitle}>{item.subtitle}</Text>
                )}
              </View>
            </View>
            
            <View style={styles.menuItemRight}>
              {item.toggle ? (
                <Switch
                  value={item.value}
                  onValueChange={item.onToggle}
                  trackColor={{ false: COLORS.lightGray, true: COLORS.primary + '30' }}
                  thumbColor={item.value ? COLORS.primary : COLORS.gray}
                />
              ) : (
                <>
                  {item.badge && (
                    <View style={styles.badge}>
                      <Text style={styles.badgeText}>{item.badge}</Text>
                    </View>
                  )}
                  <Ionicons name="chevron-forward" size={20} color={COLORS.gray} />
                </>
              )}
            </View>
          </TouchableOpacity>
        ))}
      </PremiumCard>
    </View>
  );

  const accountMenuItems = [
    {
      title: 'Edit Profile',
      subtitle: 'Update your personal information',
      icon: 'person-outline',
      iconColor: COLORS.primary,
      iconBg: COLORS.primary + '15',
      onPress: () => navigation.navigate('EditProfile'),
      disabled: !authState.isAuthenticated,
    },
    {
      title: 'Order History',
      subtitle: 'View your past orders',
      icon: 'receipt-outline',
      iconColor: COLORS.success,
      iconBg: COLORS.success + '15',
      onPress: () => navigation.navigate('OrderHistory'),
      disabled: !authState.isAuthenticated,
    },
    {
      title: 'Wishlist',
      subtitle: 'Your saved items',
      icon: 'heart-outline',
      iconColor: COLORS.error,
      iconBg: COLORS.error + '15',
      onPress: () => navigation.navigate('Wishlist'),
      badge: authState.isAuthenticated ? appState.wishlist.length : undefined,
    },
    {
      title: 'Addresses',
      subtitle: 'Manage shipping addresses',
      icon: 'location-outline',
      iconColor: COLORS.info,
      iconBg: COLORS.info + '15',
      onPress: () => Alert.alert('Coming Soon', 'Address management will be available soon.'),
      disabled: !authState.isAuthenticated,
    },
  ];

  const settingsMenuItems = [
    {
      title: 'Push Notifications',
      subtitle: 'Order updates and promotions',
      icon: 'notifications-outline',
      iconColor: COLORS.warning,
      iconBg: COLORS.warning + '15',
      toggle: true,
      value: notificationsEnabled,
      onToggle: setNotificationsEnabled,
    },
    {
      title: 'Email Updates',
      subtitle: 'Newsletter and offers',
      icon: 'mail-outline',
      iconColor: COLORS.secondary,
      iconBg: COLORS.secondary + '15',
      toggle: true,
      value: emailUpdates,
      onToggle: setEmailUpdates,
    },
    {
      title: 'Privacy Policy',
      icon: 'shield-outline',
      iconColor: COLORS.textSecondary,
      iconBg: COLORS.lightGray,
      onPress: () => Alert.alert('Privacy Policy', 'Privacy policy content would be shown here.'),
    },
    {
      title: 'Terms of Service',
      icon: 'document-text-outline',
      iconColor: COLORS.textSecondary,
      iconBg: COLORS.lightGray,
      onPress: () => Alert.alert('Terms of Service', 'Terms of service content would be shown here.'),
    },
    {
      title: 'Help & Support',
      icon: 'help-circle-outline',
      iconColor: COLORS.info,
      iconBg: COLORS.info + '15',
      onPress: () => Alert.alert('Help & Support', 'Support options would be shown here.'),
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderUserHeader()}
        {renderQuickStats()}
        
        {renderMenuSection('Account', accountMenuItems)}
        {renderMenuSection('Settings', settingsMenuItems)}
        
        {authState.isAuthenticated && (
          <View style={styles.logoutSection}>
            <AnimatedButton
              title="Logout"
              onPress={handleLogout}
              variant="outline"
              size="large"
              fullWidth
              icon={<Ionicons name="log-out-outline" size={20} color={COLORS.error} />}
              style={styles.logoutButton}
              textStyle={{ color: COLORS.error }}
            />
          </View>
        )}
        
        <View style={styles.footer}>
          <Text style={styles.footerText}>Surprise Drink v1.0.0</Text>
          <Text style={styles.footerSubtext}>Made with ❤️ for fashion lovers</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  content: {
    flex: 1,
  },
  headerCard: {
    margin: SPACING.lg,
    marginBottom: SPACING.md,
  },
  guestHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  guestAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: COLORS.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.md,
  },
  guestInfo: {
    flex: 1,
  },
  guestTitle: {
    fontSize: FONTS.lg,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  guestSubtitle: {
    fontSize: FONTS.sm,
    color: COLORS.textSecondary,
  },
  loginButton: {
    alignSelf: 'flex-start',
  },
  userHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: SPACING.md,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: COLORS.primary,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: COLORS.white,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: FONTS.xl,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  userEmail: {
    fontSize: FONTS.md,
    color: COLORS.textSecondary,
    marginBottom: SPACING.sm,
  },
  membershipBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.warning + '15',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.sm,
    alignSelf: 'flex-start',
  },
  membershipText: {
    fontSize: FONTS.xs,
    fontWeight: '600',
    color: COLORS.warning,
    marginLeft: SPACING.xs,
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: SPACING.lg,
    marginBottom: SPACING.md,
    gap: SPACING.sm,
  },
  statCard: {
    flex: 1,
  },
  statContent: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: FONTS.xl,
    fontWeight: '700',
    color: COLORS.textPrimary,
    marginTop: SPACING.xs,
  },
  statLabel: {
    fontSize: FONTS.xs,
    color: COLORS.textSecondary,
    marginTop: SPACING.xs,
  },
  menuSection: {
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    fontSize: FONTS.lg,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.md,
  },
  menuCard: {
    marginHorizontal: SPACING.lg,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  lastMenuItem: {
    borderBottomWidth: 0,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.md,
  },
  menuItemText: {
    flex: 1,
  },
  menuItemTitle: {
    fontSize: FONTS.md,
    fontWeight: '500',
    color: COLORS.textPrimary,
  },
  menuItemSubtitle: {
    fontSize: FONTS.sm,
    color: COLORS.textSecondary,
    marginTop: SPACING.xs,
  },
  menuItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  badge: {
    backgroundColor: COLORS.primary,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.sm,
  },
  badgeText: {
    fontSize: FONTS.xs,
    fontWeight: '600',
    color: COLORS.white,
  },
  logoutSection: {
    paddingHorizontal: SPACING.lg,
    marginBottom: SPACING.xl,
  },
  logoutButton: {
    borderColor: COLORS.error,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: SPACING.xl,
  },
  footerText: {
    fontSize: FONTS.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
  },
  footerSubtext: {
    fontSize: FONTS.xs,
    color: COLORS.textSecondary,
  },
});

export default EnhancedProfileScreen;
