import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';

import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../constants';
import { useAuth } from '../context/AuthContext';
import Button from '../components/common/Button';
import Input from '../components/common/Input';
import { useFormValidation, Validator } from '../utils/validation';

const RegisterScreen: React.FC = () => {
  const navigation = useNavigation();
  const { register, state, clearError } = useAuth();

  const {
    data,
    setValue,
    setFieldTouched,
    validateAll,
    getFieldError,
  } = useFormValidation(
    {
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      confirmPassword: ''
    },
    {
      firstName: [Validator.required('First name is required')],
      lastName: [Validator.required('Last name is required')],
      email: [Validator.required(), Validator.email()],
      password: [Validator.required(), Validator.minLength(6)],
      confirmPassword: [
        Validator.required('Please confirm your password'),
        Validator.confirmPassword(data.password, 'Passwords do not match')
      ],
    }
  );

  const handleRegister = async () => {
    if (!validateAll()) {
      Alert.alert('Error', 'Please fix the errors and try again');
      return;
    }

    try {
      clearError();
      await register({
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        password: data.password,
      });

      Alert.alert('Success', 'Account created successfully!', [
        { text: 'OK', onPress: () => navigation.goBack() }
      ]);
    } catch (error: any) {
      Alert.alert('Registration Failed', error.message || 'Please try again.');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <View style={styles.content}>
            <Text style={styles.title}>Create Account</Text>
            <Text style={styles.subtitle}>Join us today</Text>

            <View style={styles.form}>
              <Input
                label="First Name"
                value={data.firstName}
                onChangeText={(text) => setValue('firstName', text)}
                onBlur={() => setFieldTouched('firstName')}
                placeholder="Enter your first name"
                autoCapitalize="words"
                leftIcon="person-outline"
                error={getFieldError('firstName')}
                required
              />

              <Input
                label="Last Name"
                value={data.lastName}
                onChangeText={(text) => setValue('lastName', text)}
                onBlur={() => setFieldTouched('lastName')}
                placeholder="Enter your last name"
                autoCapitalize="words"
                leftIcon="person-outline"
                error={getFieldError('lastName')}
                required
              />

              <Input
                label="Email"
                value={data.email}
                onChangeText={(text) => setValue('email', text)}
                onBlur={() => setFieldTouched('email')}
                placeholder="Enter your email"
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
                leftIcon="mail-outline"
                error={getFieldError('email')}
                required
              />

              <Input
                label="Password"
                value={data.password}
                onChangeText={(text) => setValue('password', text)}
                onBlur={() => setFieldTouched('password')}
                placeholder="Enter your password"
                secureTextEntry
                autoCapitalize="none"
                autoCorrect={false}
                leftIcon="lock-closed-outline"
                showPasswordToggle
                error={getFieldError('password')}
                required
              />

              <Input
                label="Confirm Password"
                value={data.confirmPassword}
                onChangeText={(text) => setValue('confirmPassword', text)}
                onBlur={() => setFieldTouched('confirmPassword')}
                placeholder="Confirm your password"
                secureTextEntry
                autoCapitalize="none"
                autoCorrect={false}
                leftIcon="lock-closed-outline"
                error={getFieldError('confirmPassword')}
                required
              />

              <Button
                title="Create Account"
                onPress={handleRegister}
                fullWidth
                size="large"
                style={styles.registerButton}
                loading={state.isLoading}
                disabled={state.isLoading}
              />

              <View style={styles.loginContainer}>
                <Text style={styles.loginText}>Already have an account? </Text>
                <TouchableOpacity onPress={() => navigation.navigate('Login')}>
                  <Text style={styles.loginLink}>Sign In</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  keyboardView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
    justifyContent: 'center',
    minHeight: 600,
  },
  title: {
    fontSize: FONTS.xxxl,
    fontWeight: '700',
    color: COLORS.textPrimary,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  subtitle: {
    fontSize: FONTS.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xl,
  },
  form: {
    width: '100%',
  },
  inputContainer: {
    marginBottom: SPACING.lg,
  },
  label: {
    fontSize: FONTS.md,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  input: {
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: BORDER_RADIUS.md,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    fontSize: FONTS.md,
    color: COLORS.textPrimary,
    backgroundColor: COLORS.white,
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: COLORS.white,
  },
  passwordInput: {
    flex: 1,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    fontSize: FONTS.md,
    color: COLORS.textPrimary,
  },
  eyeButton: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
  },
  eyeText: {
    fontSize: FONTS.sm,
    color: COLORS.primary,
    fontWeight: '500',
  },
  registerButton: {
    marginBottom: SPACING.lg,
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loginText: {
    fontSize: FONTS.md,
    color: COLORS.textSecondary,
  },
  loginLink: {
    fontSize: FONTS.md,
    color: COLORS.primary,
    fontWeight: '600',
  },
});

export default RegisterScreen;
