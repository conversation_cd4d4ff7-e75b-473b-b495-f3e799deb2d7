import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Animated,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import type { NavigationProp } from '@react-navigation/native';

import { CartItem, RootStackParamList } from '../types';
import { COLORS, FONTS, SPACING, BORDER_RADIUS, SHADOWS } from '../constants';
import { useApp } from '../context/AppContext';
import { useAuth } from '../context/AuthContext';
import CachedImage from '../components/common/CachedImage';
import PremiumCard from '../components/common/PremiumCard';
import AnimatedButton from '../components/common/AnimatedButton';

const { width } = Dimensions.get('window');

const EnhancedCartScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { state, removeFromCart, updateCartQuantity, clearCart, getCartTotal } = useApp();
  const { state: authState } = useAuth();
  const { cart } = state;
  const [removingItems, setRemovingItems] = useState<Set<number>>(new Set());

  const handleQuantityChange = (productId: number, newQuantity: number) => {
    if (newQuantity < 1) {
      handleRemoveItem(productId);
    } else {
      updateCartQuantity(productId, newQuantity);
    }
  };

  const handleRemoveItem = (productId: number) => {
    Alert.alert(
      'Remove Item',
      'Are you sure you want to remove this item from your cart?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            setRemovingItems(prev => new Set(prev).add(productId));
            setTimeout(() => {
              removeFromCart(productId);
              setRemovingItems(prev => {
                const newSet = new Set(prev);
                newSet.delete(productId);
                return newSet;
              });
            }, 300);
          },
        },
      ]
    );
  };

  const handleClearCart = () => {
    Alert.alert(
      'Clear Cart',
      'Are you sure you want to remove all items from your cart?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: clearCart,
        },
      ]
    );
  };

  const handleCheckout = () => {
    if (!authState.isAuthenticated) {
      Alert.alert(
        'Login Required',
        'Please login to proceed with checkout.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Login', onPress: () => navigation.navigate('Login') },
        ]
      );
      return;
    }

    if (cart.length === 0) {
      Alert.alert('Empty Cart', 'Please add items to your cart before checkout.');
      return;
    }

    navigation.navigate('Checkout');
  };

  const renderCartItem = ({ item, index }: { item: CartItem; index: number }) => {
    const isRemoving = removingItems.has(item.product.id);
    const animatedValue = useRef(new Animated.Value(1)).current;

    if (isRemoving) {
      Animated.timing(animatedValue, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }

    return (
      <Animated.View
        style={[
          styles.cartItemContainer,
          {
            opacity: animatedValue,
            transform: [
              {
                translateX: animatedValue.interpolate({
                  inputRange: [0, 1],
                  outputRange: [-width, 0],
                }),
              },
            ],
          },
        ]}
      >
        <PremiumCard
          style={styles.cartItem}
          padding="medium"
          shadow="light"
          animated={true}
        >
          <View style={styles.itemContent}>
            <CachedImage
              uri={item.product.images[0]?.src || ''}
              style={styles.productImage}
              placeholder="https://via.placeholder.com/80x80"
            />
            
            <View style={styles.itemDetails}>
              <Text style={styles.productName} numberOfLines={2}>
                {item.product.name}
              </Text>
              
              <View style={styles.priceContainer}>
                {item.product.on_sale && (
                  <Text style={styles.originalPrice}>
                    ${item.product.regular_price}
                  </Text>
                )}
                <Text style={styles.currentPrice}>
                  ${item.product.price}
                </Text>
              </View>
              
              <View style={styles.quantityContainer}>
                <TouchableOpacity
                  style={styles.quantityButton}
                  onPress={() => handleQuantityChange(item.product.id, item.quantity - 1)}
                >
                  <Ionicons name="remove" size={16} color={COLORS.primary} />
                </TouchableOpacity>
                
                <Text style={styles.quantity}>{item.quantity}</Text>
                
                <TouchableOpacity
                  style={styles.quantityButton}
                  onPress={() => handleQuantityChange(item.product.id, item.quantity + 1)}
                >
                  <Ionicons name="add" size={16} color={COLORS.primary} />
                </TouchableOpacity>
              </View>
            </View>
            
            <View style={styles.itemActions}>
              <Text style={styles.itemTotal}>
                ${(parseFloat(item.product.price) * item.quantity).toFixed(2)}
              </Text>
              
              <TouchableOpacity
                style={styles.removeButton}
                onPress={() => handleRemoveItem(item.product.id)}
              >
                <Ionicons name="trash-outline" size={20} color={COLORS.error} />
              </TouchableOpacity>
            </View>
          </View>
        </PremiumCard>
      </Animated.View>
    );
  };

  const renderEmptyCart = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="bag-outline" size={80} color={COLORS.gray} />
      <Text style={styles.emptyTitle}>Your cart is empty</Text>
      <Text style={styles.emptySubtitle}>
        Add some products to get started
      </Text>
      <AnimatedButton
        title="Continue Shopping"
        onPress={() => navigation.navigate('Home')}
        variant="primary"
        size="large"
        style={styles.continueButton}
      />
    </View>
  );

  const renderCartSummary = () => {
    const subtotal = getCartTotal();
    const shipping = subtotal > 50 ? 0 : 9.99;
    const tax = subtotal * 0.08;
    const total = subtotal + shipping + tax;

    return (
      <PremiumCard style={styles.summaryCard} padding="large" shadow="medium">
        <Text style={styles.summaryTitle}>Order Summary</Text>
        
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Subtotal</Text>
          <Text style={styles.summaryValue}>${subtotal.toFixed(2)}</Text>
        </View>
        
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Shipping</Text>
          <Text style={[styles.summaryValue, shipping === 0 && styles.freeShipping]}>
            {shipping === 0 ? 'FREE' : `$${shipping.toFixed(2)}`}
          </Text>
        </View>
        
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Tax</Text>
          <Text style={styles.summaryValue}>${tax.toFixed(2)}</Text>
        </View>
        
        <View style={[styles.summaryRow, styles.totalRow]}>
          <Text style={styles.totalLabel}>Total</Text>
          <Text style={styles.totalValue}>${total.toFixed(2)}</Text>
        </View>
        
        {subtotal < 50 && (
          <Text style={styles.shippingNote}>
            Add ${(50 - subtotal).toFixed(2)} more for free shipping!
          </Text>
        )}
      </PremiumCard>
    );
  };

  if (cart.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Shopping Cart</Text>
        </View>
        {renderEmptyCart()}
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Shopping Cart ({cart.length})</Text>
        <TouchableOpacity onPress={handleClearCart} style={styles.clearButton}>
          <Text style={styles.clearButtonText}>Clear All</Text>
        </TouchableOpacity>
      </View>

      <FlatList
        data={cart}
        renderItem={renderCartItem}
        keyExtractor={(item) => item.product.id.toString()}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ListFooterComponent={renderCartSummary}
      />

      <View style={styles.checkoutContainer}>
        <AnimatedButton
          title={`Checkout • $${(getCartTotal() + (getCartTotal() > 50 ? 0 : 9.99) + getCartTotal() * 0.08).toFixed(2)}`}
          onPress={handleCheckout}
          variant="primary"
          size="large"
          fullWidth
          icon={<Ionicons name="card-outline" size={20} color={COLORS.white} />}
          iconPosition="right"
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    backgroundColor: COLORS.white,
    ...SHADOWS.light,
  },
  headerTitle: {
    fontSize: FONTS.xl,
    fontWeight: '600',
    color: COLORS.textPrimary,
  },
  clearButton: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
  },
  clearButtonText: {
    fontSize: FONTS.md,
    color: COLORS.error,
    fontWeight: '500',
  },
  listContainer: {
    padding: SPACING.lg,
  },
  cartItemContainer: {
    marginBottom: SPACING.md,
  },
  cartItem: {
    marginBottom: 0,
  },
  itemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  productImage: {
    width: 80,
    height: 80,
    borderRadius: BORDER_RADIUS.md,
  },
  itemDetails: {
    flex: 1,
    marginLeft: SPACING.md,
  },
  productName: {
    fontSize: FONTS.md,
    fontWeight: '500',
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  originalPrice: {
    fontSize: FONTS.sm,
    color: COLORS.textSecondary,
    textDecorationLine: 'line-through',
    marginRight: SPACING.sm,
  },
  currentPrice: {
    fontSize: FONTS.md,
    fontWeight: '600',
    color: COLORS.primary,
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantity: {
    fontSize: FONTS.md,
    fontWeight: '500',
    color: COLORS.textPrimary,
    marginHorizontal: SPACING.md,
    minWidth: 30,
    textAlign: 'center',
  },
  itemActions: {
    alignItems: 'flex-end',
  },
  itemTotal: {
    fontSize: FONTS.lg,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  removeButton: {
    padding: SPACING.sm,
  },
  summaryCard: {
    marginTop: SPACING.lg,
  },
  summaryTitle: {
    fontSize: FONTS.lg,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: SPACING.md,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  summaryLabel: {
    fontSize: FONTS.md,
    color: COLORS.textSecondary,
  },
  summaryValue: {
    fontSize: FONTS.md,
    fontWeight: '500',
    color: COLORS.textPrimary,
  },
  freeShipping: {
    color: COLORS.success,
    fontWeight: '600',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    paddingTop: SPACING.sm,
    marginTop: SPACING.sm,
  },
  totalLabel: {
    fontSize: FONTS.lg,
    fontWeight: '600',
    color: COLORS.textPrimary,
  },
  totalValue: {
    fontSize: FONTS.lg,
    fontWeight: '700',
    color: COLORS.primary,
  },
  shippingNote: {
    fontSize: FONTS.sm,
    color: COLORS.info,
    textAlign: 'center',
    marginTop: SPACING.sm,
    fontStyle: 'italic',
  },
  checkoutContainer: {
    padding: SPACING.lg,
    backgroundColor: COLORS.white,
    ...SHADOWS.light,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: SPACING.xl,
  },
  emptyTitle: {
    fontSize: FONTS.xl,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginTop: SPACING.lg,
    marginBottom: SPACING.sm,
  },
  emptySubtitle: {
    fontSize: FONTS.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xl,
  },
  continueButton: {
    marginTop: SPACING.lg,
  },
});

export default EnhancedCartScreen;
