import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { WooCommerceCategory } from '../../types';
import { COLORS, FONTS, SPACING, BORDER_RADIUS, SHADOWS, PLACEHOLDER_IMAGES } from '../../constants';
import CachedImage from '../common/CachedImage';

const { width } = Dimensions.get('window');
const CARD_WIDTH = (width - SPACING.lg * 3) / 2;

interface CategoryCardProps {
  category: WooCommerceCategory;
  onPress: () => void;
  style?: any;
  layout?: 'grid' | 'list' | 'featured';
}

const CategoryCard: React.FC<CategoryCardProps> = ({
  category,
  onPress,
  style,
  layout = 'grid',
}) => {
  const getCategoryImage = (): string => {
    if (category.image?.src) {
      return category.image.src;
    }
    return PLACEHOLDER_IMAGES.CATEGORY;
  };

  const getCardStyle = () => {
    switch (layout) {
      case 'list':
        return styles.listCard;
      case 'featured':
        return styles.featuredCard;
      default:
        return styles.gridCard;
    }
  };

  const getImageStyle = () => {
    switch (layout) {
      case 'list':
        return styles.listImage;
      case 'featured':
        return styles.featuredImage;
      default:
        return styles.gridImage;
    }
  };

  const renderGridLayout = () => (
    <TouchableOpacity
      style={[getCardStyle(), style]}
      onPress={onPress}
      activeOpacity={0.9}
    >
      <View style={styles.imageContainer}>
        <CachedImage
          uri={getCategoryImage()}
          style={getImageStyle()}
          placeholder={PLACEHOLDER_IMAGES.CATEGORY}
          showLoader={true}
          fallbackIcon="grid-outline"
        />
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.7)']}
          style={styles.gradient}
        />
        <View style={styles.overlay}>
          <Text style={styles.categoryName} numberOfLines={2}>
            {category.name}
          </Text>
          <Text style={styles.categoryCount}>
            {category.count} {category.count === 1 ? 'item' : 'items'}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderListLayout = () => (
    <TouchableOpacity
      style={[getCardStyle(), style]}
      onPress={onPress}
      activeOpacity={0.9}
    >
      <CachedImage
        uri={getCategoryImage()}
        style={getImageStyle()}
        placeholder={PLACEHOLDER_IMAGES.CATEGORY}
        showLoader={true}
        fallbackIcon="grid-outline"
      />
      <View style={styles.listContent}>
        <Text style={styles.listCategoryName} numberOfLines={1}>
          {category.name}
        </Text>
        <Text style={styles.listCategoryCount}>
          {category.count} {category.count === 1 ? 'item' : 'items'}
        </Text>
        {category.description && (
          <Text style={styles.categoryDescription} numberOfLines={2}>
            {category.description}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderFeaturedLayout = () => (
    <TouchableOpacity
      style={[getCardStyle(), style]}
      onPress={onPress}
      activeOpacity={0.9}
    >
      <CachedImage
        uri={getCategoryImage()}
        style={getImageStyle()}
        placeholder={PLACEHOLDER_IMAGES.CATEGORY}
        showLoader={true}
        fallbackIcon="grid-outline"
      />
      <LinearGradient
        colors={['transparent', 'rgba(0,0,0,0.8)']}
        style={styles.featuredGradient}
      />
      <View style={styles.featuredOverlay}>
        <Text style={styles.featuredCategoryName} numberOfLines={2}>
          {category.name}
        </Text>
        <Text style={styles.featuredCategoryCount}>
          {category.count} {category.count === 1 ? 'item' : 'items'}
        </Text>
        {category.description && (
          <Text style={styles.featuredDescription} numberOfLines={3}>
            {category.description}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );

  switch (layout) {
    case 'list':
      return renderListLayout();
    case 'featured':
      return renderFeaturedLayout();
    default:
      return renderGridLayout();
  }
};

const styles = StyleSheet.create({
  // Grid layout styles
  gridCard: {
    width: CARD_WIDTH,
    height: CARD_WIDTH * 1.2,
    borderRadius: BORDER_RADIUS.md,
    overflow: 'hidden',
    ...SHADOWS.medium,
  },
  gridImage: {
    width: '100%',
    height: '100%',
  },
  imageContainer: {
    position: 'relative',
    width: '100%',
    height: '100%',
  },
  gradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '60%',
  },
  overlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: SPACING.md,
  },
  categoryName: {
    fontSize: FONTS.lg,
    fontWeight: '700',
    color: COLORS.white,
    marginBottom: SPACING.xs,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  categoryCount: {
    fontSize: FONTS.sm,
    color: COLORS.white,
    opacity: 0.9,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },

  // List layout styles
  listCard: {
    flexDirection: 'row',
    backgroundColor: COLORS.white,
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    ...SHADOWS.light,
  },
  listImage: {
    width: 80,
    height: 80,
    borderRadius: BORDER_RADIUS.sm,
  },
  listContent: {
    flex: 1,
    marginLeft: SPACING.md,
    justifyContent: 'center',
  },
  listCategoryName: {
    fontSize: FONTS.lg,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  listCategoryCount: {
    fontSize: FONTS.sm,
    color: COLORS.primary,
    fontWeight: '500',
    marginBottom: SPACING.xs,
  },
  categoryDescription: {
    fontSize: FONTS.sm,
    color: COLORS.textSecondary,
    lineHeight: FONTS.lineHeight.sm,
  },

  // Featured layout styles
  featuredCard: {
    width: width - SPACING.lg * 2,
    height: 200,
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
    marginHorizontal: SPACING.lg,
    ...SHADOWS.heavy,
  },
  featuredImage: {
    width: '100%',
    height: '100%',
  },
  featuredGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '70%',
  },
  featuredOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: SPACING.lg,
  },
  featuredCategoryName: {
    fontSize: FONTS.xxl,
    fontWeight: '700',
    color: COLORS.white,
    marginBottom: SPACING.sm,
    textShadowColor: 'rgba(0,0,0,0.7)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  featuredCategoryCount: {
    fontSize: FONTS.md,
    color: COLORS.white,
    opacity: 0.9,
    marginBottom: SPACING.sm,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  featuredDescription: {
    fontSize: FONTS.sm,
    color: COLORS.white,
    opacity: 0.8,
    lineHeight: FONTS.lineHeight.sm,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
});

export default CategoryCard;
