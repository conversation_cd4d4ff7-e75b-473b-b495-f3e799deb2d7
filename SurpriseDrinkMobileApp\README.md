# Surprise Drink Mobile App

A premium React Native mobile application built with Expo for the Surprise Drink WooCommerce store. This app provides a beautiful, user-friendly interface for browsing and purchasing lingerie and fashion products.

## Features

### 🛍️ Core E-commerce Features
- **Product Catalog**: Browse products with beautiful card layouts
- **Categories**: Organized product categories with filtering
- **Search**: Powerful search functionality with real-time results
- **Product Details**: Detailed product pages with image galleries
- **Shopping Cart**: Add, remove, and manage cart items
- **Wishlist**: Save favorite products for later
- **User Authentication**: Login and registration system

### 🎨 Premium UI/UX
- **Modern Design**: Clean, premium design suitable for fashion/lingerie
- **Responsive Layout**: Optimized for all screen sizes
- **Smooth Animations**: Fluid transitions and interactions
- **Custom Components**: Reusable UI components with consistent styling
- **Dark/Light Theme**: Support for different color schemes

### 🔧 Technical Features
- **TypeScript**: Full type safety and better development experience
- **Context API**: Centralized state management
- **AsyncStorage**: Persistent local storage for cart and user data
- **Navigation**: Stack and tab navigation with React Navigation
- **API Integration**: Full WooCommerce REST API integration

## Tech Stack

- **React Native**: 0.79.5
- **Expo**: ~53.0.17
- **TypeScript**: ~5.8.3
- **React Navigation**: 7.x
- **Axios**: HTTP client for API calls
- **AsyncStorage**: Local data persistence
- **Expo Vector Icons**: Beautiful icon set

## Installation

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn
- Expo CLI (`npm install -g @expo/cli`)
- Expo Go app on your mobile device (for testing)

### Setup Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd SurpriseDrinkMobileApp
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure API credentials**
   Update the WooCommerce API credentials in `src/services/woocommerce.ts`:
   ```typescript
   this.baseURL = 'https://your-woocommerce-site.com';
   this.consumerKey = 'your_consumer_key';
   this.consumerSecret = 'your_consumer_secret';
   ```

4. **Start the development server**
   ```bash
   npm start
   ```

5. **Run on device/simulator**
   - Scan the QR code with Expo Go app (Android/iOS)
   - Or press `a` for Android emulator
   - Or press `i` for iOS simulator

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── common/         # Generic components (Button, Input, etc.)
│   └── product/        # Product-specific components
├── constants/          # App constants and configuration
├── context/           # React Context for state management
├── navigation/        # Navigation configuration
├── screens/          # Screen components
├── services/         # API services and utilities
└── types/           # TypeScript type definitions
```

## API Configuration

The app is configured to work with WooCommerce REST API v3. Make sure your WooCommerce store has:

1. **REST API enabled**
2. **Consumer Key and Secret generated**
3. **CORS configured** for mobile app access
4. **SSL certificate** (HTTPS required)

### Required WooCommerce Endpoints
- `GET /wp-json/wc/v3/products` - Product listing
- `GET /wp-json/wc/v3/products/{id}` - Product details
- `GET /wp-json/wc/v3/products/categories` - Categories
- `POST /wp-json/wc/v3/customers` - Customer registration
- `POST /wp-json/wc/v3/orders` - Order creation

## Customization

### Colors and Theming
Update colors in `src/constants/index.ts`:
```typescript
export const COLORS = {
  primary: '#E91E63',    // Main brand color
  secondary: '#9C27B0',  // Secondary color
  // ... other colors
};
```

### App Configuration
Modify app settings in `src/constants/index.ts`:
```typescript
export const APP_CONFIG = {
  APP_NAME: 'Your App Name',
  CURRENCY: 'USD',
  // ... other settings
};
```

## Building for Production

### Android
```bash
expo build:android
```

### iOS
```bash
expo build:ios
```

### Web
```bash
npm run web
```

## Features Implementation Status

### ✅ Completed
- [x] Project setup with TypeScript
- [x] Navigation structure (Tab + Stack)
- [x] WooCommerce API integration
- [x] Product catalog and search
- [x] Shopping cart functionality
- [x] Wishlist feature
- [x] User authentication UI
- [x] Premium UI components
- [x] State management with Context API

### 🚧 In Progress
- [ ] Payment integration
- [ ] Order management
- [ ] Push notifications
- [ ] Offline support

### 📋 Planned
- [ ] Social login (Google, Facebook)
- [ ] Product reviews and ratings
- [ ] Advanced filtering
- [ ] Multi-language support
- [ ] Analytics integration

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Contact: [<EMAIL>]

## Acknowledgments

- WooCommerce REST API documentation
- React Native community
- Expo team for the amazing development platform
- All contributors and testers

---

**Note**: This app is designed specifically for the Surprise Drink WooCommerce store but can be easily adapted for any WooCommerce-based e-commerce site by updating the API configuration and branding elements.
