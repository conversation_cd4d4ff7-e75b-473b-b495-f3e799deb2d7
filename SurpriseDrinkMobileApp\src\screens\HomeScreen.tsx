import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  RefreshControl,
  Dimensions,
  TouchableOpacity,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';

import { WooCommerceProduct, WooCommerceCategory } from '../types';
import { COLORS, FONTS, SPACING, BORDER_RADIUS, SHADOWS } from '../constants';
import { useApp } from '../context/AppContext';
import wooCommerceAPI from '../services/woocommerce';
import ProductCard from '../components/product/ProductCard';
import Button from '../components/common/Button';

const { width } = Dimensions.get('window');

const HomeScreen: React.FC = () => {
  const navigation = useNavigation();
  const { setLoading, setError } = useApp();
  
  const [featuredProducts, setFeaturedProducts] = useState<WooCommerceProduct[]>([]);
  const [latestProducts, setLatestProducts] = useState<WooCommerceProduct[]>([]);
  const [onSaleProducts, setOnSaleProducts] = useState<WooCommerceProduct[]>([]);
  const [categories, setCategories] = useState<WooCommerceCategory[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadHomeData();
  }, []);

  const loadHomeData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [featured, latest, onSale, categoriesData] = await Promise.all([
        wooCommerceAPI.getFeaturedProducts(),
        wooCommerceAPI.getLatestProducts(),
        wooCommerceAPI.getOnSaleProducts(),
        wooCommerceAPI.getCategories({ per_page: 6 }),
      ]);

      setFeaturedProducts(featured);
      setLatestProducts(latest);
      setOnSaleProducts(onSale);
      setCategories(categoriesData);
    } catch (error) {
      console.error('Error loading home data:', error);
      setError('Failed to load home data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadHomeData();
    setRefreshing(false);
  };

  const navigateToProduct = (productId: number) => {
    navigation.navigate('ProductDetail', { productId });
  };

  const navigateToCategory = (categoryId: number, categoryName: string) => {
    navigation.navigate('Category', { categoryId, categoryName });
  };

  const navigateToSearch = () => {
    navigation.navigate('Search');
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.headerTop}>
        <View>
          <Text style={styles.welcomeText}>Welcome to</Text>
          <Text style={styles.appName}>Surprise Drink</Text>
        </View>
        <TouchableOpacity
          style={styles.searchButton}
          onPress={navigateToSearch}
        >
          <Ionicons name="search" size={24} color={COLORS.primary} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderBanner = () => (
    <View style={styles.bannerContainer}>
      <LinearGradient
        colors={[COLORS.primary, COLORS.primaryDark]}
        style={styles.banner}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.bannerContent}>
          <Text style={styles.bannerTitle}>Premium Lingerie</Text>
          <Text style={styles.bannerSubtitle}>Discover our exclusive collection</Text>
          <Button
            title="Shop Now"
            onPress={() => navigation.navigate('Categories')}
            variant="outline"
            style={styles.bannerButton}
            textStyle={{ color: COLORS.white }}
          />
        </View>
        <View style={styles.bannerImageContainer}>
          <Image
            source={{
              uri: 'https://surprisedrink.s2-tastewp.com/wp-content/uploads/2021/09/lingerie-girl-collage.png'
            }}
            style={styles.bannerImage}
            resizeMode="contain"
          />
        </View>
      </LinearGradient>
    </View>
  );

  const renderCategories = () => (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Shop by Category</Text>
        <TouchableOpacity onPress={() => navigation.navigate('Categories')}>
          <Text style={styles.seeAllText}>See All</Text>
        </TouchableOpacity>
      </View>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.categoriesContainer}
      >
        {categories.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={styles.categoryCard}
            onPress={() => navigateToCategory(category.id, category.name)}
          >
            <View style={styles.categoryImageContainer}>
              <Image
                source={{
                  uri: category.image?.src || 'https://via.placeholder.com/100x100/E91E63/FFFFFF?text=Category'
                }}
                style={styles.categoryImage}
                resizeMode="cover"
              />
            </View>
            <Text style={styles.categoryName} numberOfLines={2}>
              {category.name}
            </Text>
            <Text style={styles.categoryCount}>
              {category.count} items
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderProductSection = (
    title: string,
    products: WooCommerceProduct[],
    onSeeAll: () => void
  ) => (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>{title}</Text>
        <TouchableOpacity onPress={onSeeAll}>
          <Text style={styles.seeAllText}>See All</Text>
        </TouchableOpacity>
      </View>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.productsContainer}
      >
        {products.map((product) => (
          <ProductCard
            key={product.id}
            product={product}
            onPress={() => navigateToProduct(product.id)}
            style={styles.productCard}
          />
        ))}
      </ScrollView>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderHeader()}
        {renderBanner()}
        {renderCategories()}
        
        {featuredProducts.length > 0 &&
          renderProductSection(
            'Featured Products',
            featuredProducts,
            () => navigation.navigate('Search', { featured: true })
          )}
        
        {onSaleProducts.length > 0 &&
          renderProductSection(
            'On Sale',
            onSaleProducts,
            () => navigation.navigate('Search', { onSale: true })
          )}
        
        {latestProducts.length > 0 &&
          renderProductSection(
            'Latest Products',
            latestProducts,
            () => navigation.navigate('Search', { latest: true })
          )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    backgroundColor: COLORS.white,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    ...SHADOWS.light,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  welcomeText: {
    fontSize: FONTS.sm,
    color: COLORS.textSecondary,
  },
  appName: {
    fontSize: FONTS.xl,
    fontWeight: '700',
    color: COLORS.primary,
  },
  searchButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: COLORS.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
  },
  bannerContainer: {
    marginHorizontal: SPACING.lg,
    marginVertical: SPACING.md,
  },
  banner: {
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.lg,
    flexDirection: 'row',
    alignItems: 'center',
    minHeight: 140,
  },
  bannerContent: {
    flex: 1,
  },
  bannerTitle: {
    fontSize: FONTS.xxl,
    fontWeight: '700',
    color: COLORS.white,
    marginBottom: SPACING.xs,
  },
  bannerSubtitle: {
    fontSize: FONTS.md,
    color: COLORS.white,
    opacity: 0.9,
    marginBottom: SPACING.md,
  },
  bannerButton: {
    alignSelf: 'flex-start',
    borderColor: COLORS.white,
  },
  bannerImageContainer: {
    width: 100,
    height: 100,
  },
  bannerImage: {
    width: '100%',
    height: '100%',
  },
  section: {
    marginBottom: SPACING.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    marginBottom: SPACING.md,
  },
  sectionTitle: {
    fontSize: FONTS.lg,
    fontWeight: '600',
    color: COLORS.textPrimary,
  },
  seeAllText: {
    fontSize: FONTS.md,
    color: COLORS.primary,
    fontWeight: '500',
  },
  categoriesContainer: {
    paddingHorizontal: SPACING.lg,
  },
  categoryCard: {
    width: 80,
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  categoryImageContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    overflow: 'hidden',
    marginBottom: SPACING.sm,
    ...SHADOWS.light,
  },
  categoryImage: {
    width: '100%',
    height: '100%',
  },
  categoryName: {
    fontSize: FONTS.sm,
    fontWeight: '500',
    color: COLORS.textPrimary,
    textAlign: 'center',
    marginBottom: SPACING.xs,
  },
  categoryCount: {
    fontSize: FONTS.xs,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  productsContainer: {
    paddingHorizontal: SPACING.lg,
  },
  productCard: {
    marginRight: SPACING.md,
  },
});

export default HomeScreen;
