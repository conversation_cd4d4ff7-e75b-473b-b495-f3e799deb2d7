{"expo": {"name": "Surprise Drink", "slug": "surprise-drink-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "newArchEnabled": true, "scheme": "<PERSON><PERSON><PERSON>", "description": "Premium fashion and lifestyle mobile shopping app", "keywords": ["fashion", "shopping", "ecommerce", "lifestyle", "mobile"], "primaryColor": "#E91E63", "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#E91E63"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.surprisedrink.app", "buildNumber": "1", "infoPlist": {"NSCameraUsageDescription": "This app uses the camera to take photos for your profile.", "NSPhotoLibraryUsageDescription": "This app accesses your photo library to select images.", "NSLocationWhenInUseUsageDescription": "This app uses location to provide better shipping estimates."}, "config": {"usesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#E91E63"}, "edgeToEdgeEnabled": true, "package": "com.surprisedrink.app", "versionCode": 1, "permissions": ["CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "INTERNET", "ACCESS_NETWORK_STATE"]}, "web": {"favicon": "./assets/favicon.png", "name": "Surprise Drink", "shortName": "Surprise Drink", "lang": "en", "scope": "/", "themeColor": "#E91E63", "backgroundColor": "#ffffff"}, "plugins": [["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them.", "cameraPermission": "The app accesses your camera to let you take photos."}]], "extra": {"eas": {"projectId": "surprise-drink-mobile"}}}}