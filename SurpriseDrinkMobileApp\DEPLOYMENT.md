# Deployment Guide - Surprise Drink Mobile App

This guide covers the deployment process for the Surprise Drink mobile application built with React Native and Expo.

## Prerequisites

Before deploying, ensure you have:

1. **Expo CLI** installed globally: `npm install -g @expo/cli`
2. **EAS CLI** installed globally: `npm install -g eas-cli`
3. **Expo account** created at [expo.dev](https://expo.dev)
4. **Apple Developer Account** (for iOS deployment)
5. **Google Play Console Account** (for Android deployment)

## Environment Setup

### 1. Configure API Endpoints

Update the API configuration in `src/services/woocommerce.ts`:

```typescript
this.baseURL = 'https://your-production-domain.com';
this.consumerKey = 'your_production_consumer_key';
this.consumerSecret = 'your_production_consumer_secret';
```

### 2. Update App Configuration

Modify `app.json` with your production settings:

```json
{
  "expo": {
    "name": "Your App Name",
    "slug": "your-app-slug",
    "version": "1.0.0",
    "ios": {
      "bundleIdentifier": "com.yourcompany.yourapp"
    },
    "android": {
      "package": "com.yourcompany.yourapp"
    }
  }
}
```

## Building for Production

### 1. Initialize EAS

```bash
cd SurpriseDrinkMobileApp
eas login
eas build:configure
```

### 2. Build for Android

```bash
# Development build
eas build --platform android --profile development

# Production build
eas build --platform android --profile production
```

### 3. Build for iOS

```bash
# Development build
eas build --platform ios --profile development

# Production build
eas build --platform ios --profile production
```

### 4. Build for Both Platforms

```bash
eas build --platform all --profile production
```

## App Store Deployment

### iOS App Store

1. **Prepare App Store Connect**
   - Create app record in App Store Connect
   - Configure app information, pricing, and availability
   - Add app screenshots and metadata

2. **Upload Build**
   ```bash
   eas submit --platform ios
   ```

3. **Review Process**
   - Submit for App Store review
   - Respond to any feedback from Apple
   - Release when approved

### Google Play Store

1. **Prepare Google Play Console**
   - Create app in Google Play Console
   - Configure store listing
   - Add screenshots and app description

2. **Upload Build**
   ```bash
   eas submit --platform android
   ```

3. **Release Process**
   - Create release in Play Console
   - Submit for review
   - Release to production

## Environment Variables

Create environment-specific configurations:

### Development
```typescript
// src/config/development.ts
export const config = {
  API_BASE_URL: 'https://staging.yoursite.com',
  CONSUMER_KEY: 'staging_key',
  CONSUMER_SECRET: 'staging_secret',
  ANALYTICS_ENABLED: false,
};
```

### Production
```typescript
// src/config/production.ts
export const config = {
  API_BASE_URL: 'https://yoursite.com',
  CONSUMER_KEY: 'production_key',
  CONSUMER_SECRET: 'production_secret',
  ANALYTICS_ENABLED: true,
};
```

## Performance Optimization

### 1. Bundle Size Optimization

```bash
# Analyze bundle size
npx expo install --fix
expo doctor
```

### 2. Image Optimization

- Compress images before including in the app
- Use appropriate image formats (WebP for Android, HEIC for iOS)
- Implement lazy loading for product images

### 3. Code Splitting

- Use React.lazy() for screen components
- Implement dynamic imports for large libraries

## Testing Before Deployment

### 1. Device Testing

```bash
# Test on physical devices
expo start --dev-client
```

### 2. Performance Testing

- Test app performance on low-end devices
- Monitor memory usage and CPU performance
- Test network connectivity scenarios

### 3. User Acceptance Testing

- Test all user flows
- Verify payment integration
- Test offline functionality

## Monitoring and Analytics

### 1. Crash Reporting

Add crash reporting service (e.g., Sentry):

```bash
npm install @sentry/react-native
```

### 2. Analytics

Implement analytics tracking:

```bash
expo install expo-analytics-amplitude
# or
expo install expo-firebase-analytics
```

### 3. Performance Monitoring

Monitor app performance in production:

- Track API response times
- Monitor app startup time
- Track user engagement metrics

## Security Considerations

### 1. API Security

- Use HTTPS for all API calls
- Implement proper authentication
- Validate all user inputs

### 2. Data Protection

- Encrypt sensitive data in AsyncStorage
- Implement proper session management
- Follow GDPR/privacy regulations

### 3. Code Obfuscation

```bash
# Enable code obfuscation for production builds
eas build --platform all --profile production --clear-cache
```

## Continuous Integration/Deployment

### 1. GitHub Actions

Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy to App Stores
on:
  push:
    tags:
      - 'v*'

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '16'
      - run: npm install
      - run: eas build --platform all --profile production --non-interactive
      - run: eas submit --platform all --non-interactive
```

### 2. Automated Testing

```yaml
- name: Run tests
  run: |
    npm test
    npm run lint
    npm run type-check
```

## Post-Deployment

### 1. Monitor App Performance

- Check crash reports
- Monitor user feedback
- Track key metrics

### 2. Update Strategy

- Plan regular updates
- Implement over-the-air updates with Expo Updates
- Maintain backward compatibility

### 3. User Support

- Set up customer support channels
- Create user documentation
- Monitor app store reviews

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check dependencies compatibility
   - Verify certificates and provisioning profiles
   - Clear cache: `eas build --clear-cache`

2. **App Store Rejection**
   - Review App Store guidelines
   - Fix metadata and screenshots
   - Address technical issues

3. **Performance Issues**
   - Optimize images and assets
   - Implement code splitting
   - Use performance profiling tools

### Support Resources

- [Expo Documentation](https://docs.expo.dev/)
- [React Native Documentation](https://reactnative.dev/)
- [WooCommerce REST API](https://woocommerce.github.io/woocommerce-rest-api-docs/)

## Checklist

Before deploying to production:

- [ ] API endpoints configured for production
- [ ] App icons and splash screens updated
- [ ] App store metadata prepared
- [ ] Privacy policy and terms of service ready
- [ ] Testing completed on multiple devices
- [ ] Performance optimization implemented
- [ ] Analytics and crash reporting configured
- [ ] Security review completed
- [ ] Backup and rollback plan prepared

---

For additional support or questions, refer to the project documentation or contact the development team.
