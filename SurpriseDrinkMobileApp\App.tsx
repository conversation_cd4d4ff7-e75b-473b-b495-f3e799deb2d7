import React from 'react';
import { StatusBar } from 'expo-status-bar';
import Toast from 'react-native-toast-message';

import { AppProvider } from './src/context/AppContext';
import AppNavigator from './src/navigation/AppNavigator';
import { COLORS } from './src/constants';

export default function App() {
  return (
    <AppProvider>
      <StatusBar style="light" backgroundColor={COLORS.primary} />
      <AppNavigator />
      <Toast />
    </AppProvider>
  );
}
