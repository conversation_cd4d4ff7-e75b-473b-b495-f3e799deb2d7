import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';

import { COLORS, FONTS, SPACING } from '../constants';
import { useAuth } from '../context/AuthContext';
import Button from '../components/common/Button';
import Input from '../components/common/Input';
import { useFormValidation, Validator } from '../utils/validation';

const ChangePasswordScreen: React.FC = () => {
  const navigation = useNavigation();
  const { changePassword, state, clearError } = useAuth();

  const {
    data,
    setValue,
    setFieldTouched,
    validateAll,
    getFieldError,
    reset,
  } = useFormValidation(
    { 
      currentPassword: '', 
      newPassword: '', 
      confirmPassword: '' 
    },
    {
      currentPassword: [Validator.required('Current password is required')],
      newPassword: [
        Validator.required('New password is required'),
        Validator.minLength(6, 'Password must be at least 6 characters')
      ],
      confirmPassword: [
        Validator.required('Please confirm your new password'),
        Validator.confirmPassword(data.newPassword, 'Passwords do not match')
      ],
    }
  );

  const handleChangePassword = async () => {
    if (!validateAll()) {
      Alert.alert('Error', 'Please fix the errors and try again');
      return;
    }

    try {
      clearError();
      await changePassword(data.currentPassword, data.newPassword);
      
      Alert.alert(
        'Success', 
        'Password changed successfully!',
        [
          { 
            text: 'OK', 
            onPress: () => {
              reset();
              navigation.goBack();
            }
          }
        ]
      );
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to change password.');
    }
  };

  const handleCancel = () => {
    reset();
    navigation.goBack();
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <View style={styles.content}>
            <Text style={styles.title}>Change Password</Text>
            <Text style={styles.subtitle}>
              Enter your current password and choose a new one
            </Text>

            <View style={styles.form}>
              <Input
                label="Current Password"
                value={data.currentPassword}
                onChangeText={(text) => setValue('currentPassword', text)}
                onBlur={() => setFieldTouched('currentPassword')}
                placeholder="Enter your current password"
                secureTextEntry
                autoCapitalize="none"
                autoCorrect={false}
                leftIcon="lock-closed-outline"
                showPasswordToggle
                error={getFieldError('currentPassword')}
                required
              />

              <Input
                label="New Password"
                value={data.newPassword}
                onChangeText={(text) => setValue('newPassword', text)}
                onBlur={() => setFieldTouched('newPassword')}
                placeholder="Enter your new password"
                secureTextEntry
                autoCapitalize="none"
                autoCorrect={false}
                leftIcon="lock-closed-outline"
                showPasswordToggle
                error={getFieldError('newPassword')}
                required
              />

              <Input
                label="Confirm New Password"
                value={data.confirmPassword}
                onChangeText={(text) => setValue('confirmPassword', text)}
                onBlur={() => setFieldTouched('confirmPassword')}
                placeholder="Confirm your new password"
                secureTextEntry
                autoCapitalize="none"
                autoCorrect={false}
                leftIcon="lock-closed-outline"
                error={getFieldError('confirmPassword')}
                required
              />

              <View style={styles.passwordRequirements}>
                <Text style={styles.requirementsTitle}>Password Requirements:</Text>
                <Text style={styles.requirement}>• At least 6 characters long</Text>
                <Text style={styles.requirement}>• Mix of letters and numbers recommended</Text>
                <Text style={styles.requirement}>• Avoid using personal information</Text>
              </View>

              <View style={styles.buttonContainer}>
                <Button
                  title="Change Password"
                  onPress={handleChangePassword}
                  fullWidth
                  size="large"
                  style={styles.changeButton}
                  loading={state.isLoading}
                  disabled={state.isLoading}
                />

                <Button
                  title="Cancel"
                  onPress={handleCancel}
                  variant="outline"
                  fullWidth
                  size="large"
                  style={styles.cancelButton}
                  disabled={state.isLoading}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  keyboardView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
    paddingTop: SPACING.xl,
  },
  title: {
    fontSize: FONTS.xxl,
    fontWeight: '700',
    color: COLORS.textPrimary,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  subtitle: {
    fontSize: FONTS.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xl,
  },
  form: {
    width: '100%',
  },
  passwordRequirements: {
    backgroundColor: COLORS.lightGray,
    padding: SPACING.md,
    borderRadius: SPACING.sm,
    marginVertical: SPACING.lg,
  },
  requirementsTitle: {
    fontSize: FONTS.md,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  requirement: {
    fontSize: FONTS.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
  },
  buttonContainer: {
    marginTop: SPACING.lg,
  },
  changeButton: {
    marginBottom: SPACING.md,
  },
  cancelButton: {
    borderColor: COLORS.gray,
  },
});

export default ChangePasswordScreen;
