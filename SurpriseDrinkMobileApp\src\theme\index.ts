import { Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

// Enhanced color palette
export const COLORS = {
  // Primary colors
  primary: '#E91E63',
  primaryLight: '#F8BBD9',
  primaryDark: '#AD1457',
  
  // Secondary colors
  secondary: '#9C27B0',
  secondaryLight: '#E1BEE7',
  secondaryDark: '#6A1B9A',
  
  // Accent colors
  accent: '#FF5722',
  accentLight: '#FFCCBC',
  accentDark: '#D84315',
  
  // Neutral colors
  white: '#FFFFFF',
  black: '#000000',
  gray: '#9E9E9E',
  lightGray: '#F5F5F5',
  darkGray: '#424242',
  
  // Text colors
  textPrimary: '#212121',
  textSecondary: '#757575',
  textLight: '#FFFFFF',
  textMuted: '#BDBDBD',
  
  // Background colors
  background: '#FAFAFA',
  surface: '#FFFFFF',
  overlay: 'rgba(0, 0, 0, 0.5)',
  
  // Status colors
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',
  
  // Gradient colors
  gradients: {
    primary: ['#E91E63', '#9C27B0'],
    secondary: ['#FF5722', '#E91E63'],
    sunset: ['#FF6B6B', '#FFE66D'],
    ocean: ['#667eea', '#764ba2'],
    forest: ['#134E5E', '#71B280'],
  },
  
  // Border colors
  border: '#E0E0E0',
  borderLight: '#F0F0F0',
  borderDark: '#BDBDBD',
};

// Typography system
export const TYPOGRAPHY = {
  // Font families
  fontFamily: {
    regular: 'System',
    medium: 'System',
    semiBold: 'System',
    bold: 'System',
  },
  
  // Font sizes
  fontSize: {
    xs: 10,
    sm: 12,
    md: 14,
    lg: 16,
    xl: 18,
    xxl: 20,
    xxxl: 24,
    display: 32,
  },
  
  // Font weights
  fontWeight: {
    light: '300',
    regular: '400',
    medium: '500',
    semiBold: '600',
    bold: '700',
    extraBold: '800',
  },
  
  // Line heights
  lineHeight: {
    xs: 14,
    sm: 16,
    md: 20,
    lg: 24,
    xl: 28,
    xxl: 32,
    xxxl: 36,
    display: 40,
  },
  
  // Letter spacing
  letterSpacing: {
    tight: -0.5,
    normal: 0,
    wide: 0.5,
    wider: 1,
  },
};

// Spacing system
export const SPACING = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  xxl: 24,
  xxxl: 32,
  xxxxl: 40,
};

// Border radius system
export const BORDER_RADIUS = {
  none: 0,
  xs: 2,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  xxl: 20,
  full: 9999,
};

// Shadow system
export const SHADOWS = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  light: {
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  medium: {
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4,
  },
  heavy: {
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
  intense: {
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 16,
  },
};

// Layout dimensions
export const LAYOUT = {
  window: {
    width,
    height,
  },
  isSmallDevice: width < 375,
  isLargeDevice: width >= 414,
  headerHeight: 56,
  tabBarHeight: 60,
  statusBarHeight: 44,
  bottomSafeArea: 34,
};

// Animation timings
export const ANIMATIONS = {
  timing: {
    fast: 150,
    normal: 250,
    slow: 350,
    slower: 500,
  },
  easing: {
    linear: 'linear',
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
  },
};

// Component variants
export const VARIANTS = {
  button: {
    primary: {
      backgroundColor: COLORS.primary,
      color: COLORS.white,
    },
    secondary: {
      backgroundColor: COLORS.secondary,
      color: COLORS.white,
    },
    outline: {
      backgroundColor: 'transparent',
      borderColor: COLORS.primary,
      color: COLORS.primary,
    },
    ghost: {
      backgroundColor: 'transparent',
      color: COLORS.primary,
    },
  },
  card: {
    default: {
      backgroundColor: COLORS.white,
      shadow: SHADOWS.light,
    },
    elevated: {
      backgroundColor: COLORS.white,
      shadow: SHADOWS.medium,
    },
    outlined: {
      backgroundColor: COLORS.white,
      borderColor: COLORS.border,
      borderWidth: 1,
    },
  },
};

// Breakpoints for responsive design
export const BREAKPOINTS = {
  xs: 0,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
};

// Z-index layers
export const Z_INDEX = {
  base: 0,
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modalBackdrop: 1040,
  modal: 1050,
  popover: 1060,
  tooltip: 1070,
  toast: 1080,
};

// Default theme object
export const THEME = {
  colors: COLORS,
  typography: TYPOGRAPHY,
  spacing: SPACING,
  borderRadius: BORDER_RADIUS,
  shadows: SHADOWS,
  layout: LAYOUT,
  animations: ANIMATIONS,
  variants: VARIANTS,
  breakpoints: BREAKPOINTS,
  zIndex: Z_INDEX,
};

export default THEME;
