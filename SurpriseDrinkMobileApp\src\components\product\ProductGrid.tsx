import React from 'react';
import {
  FlatList,
  View,
  StyleSheet,
  RefreshControl,
  ActivityIndicator,
  Text,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { WooCommerceProduct } from '../../types';
import { COLORS, FONTS, SPACING } from '../../constants';
import ProductCard from './ProductCard';

const { width } = Dimensions.get('window');
const CARD_WIDTH = (width - SPACING.lg * 3) / 2;

interface ProductGridProps {
  products: WooCommerceProduct[];
  loading?: boolean;
  refreshing?: boolean;
  onRefresh?: () => void;
  onLoadMore?: () => void;
  onProductPress: (productId: number) => void;
  numColumns?: number;
  showAddToCart?: boolean;
  emptyMessage?: string;
  emptyIcon?: keyof typeof Ionicons.glyphMap;
  ListHeaderComponent?: React.ComponentType<any> | React.ReactElement | null;
  ListFooterComponent?: React.ComponentType<any> | React.ReactElement | null;
}

const ProductGrid: React.FC<ProductGridProps> = ({
  products,
  loading = false,
  refreshing = false,
  onRefresh,
  onLoadMore,
  onProductPress,
  numColumns = 2,
  showAddToCart = true,
  emptyMessage = 'No products found',
  emptyIcon = 'bag-outline',
  ListHeaderComponent,
  ListFooterComponent,
}) => {
  const renderProduct = ({ item, index }: { item: WooCommerceProduct; index: number }) => (
    <ProductCard
      product={item}
      onPress={() => onProductPress(item.id)}
      showAddToCart={showAddToCart}
      style={[
        styles.productCard,
        numColumns === 2 && index % 2 === 0 ? styles.leftCard : styles.rightCard,
        numColumns === 1 && styles.fullWidthCard,
      ]}
    />
  );

  const renderEmpty = () => {
    if (loading) {
      return (
        <View style={styles.centerContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loadingText}>Loading products...</Text>
        </View>
      );
    }

    return (
      <View style={styles.centerContainer}>
        <Ionicons name={emptyIcon} size={80} color={COLORS.gray} />
        <Text style={styles.emptyTitle}>{emptyMessage}</Text>
        <Text style={styles.emptySubtitle}>
          Try adjusting your search or filters
        </Text>
      </View>
    );
  };

  const renderFooter = () => {
    if (ListFooterComponent) {
      return ListFooterComponent;
    }

    if (loading && products.length > 0) {
      return (
        <View style={styles.footerLoader}>
          <ActivityIndicator size="small" color={COLORS.primary} />
          <Text style={styles.footerText}>Loading more...</Text>
        </View>
      );
    }

    return null;
  };

  const keyExtractor = (item: WooCommerceProduct, index: number) => 
    `product-${item.id}-${index}`;

  const getItemLayout = (data: any, index: number) => {
    const itemHeight = CARD_WIDTH * 1.4; // Approximate card height
    return {
      length: itemHeight,
      offset: itemHeight * Math.floor(index / numColumns),
      index,
    };
  };

  return (
    <FlatList
      data={products}
      renderItem={renderProduct}
      keyExtractor={keyExtractor}
      numColumns={numColumns}
      key={numColumns} // Force re-render when columns change
      contentContainerStyle={[
        styles.container,
        products.length === 0 && styles.emptyContainer,
      ]}
      columnWrapperStyle={numColumns > 1 ? styles.row : undefined}
      showsVerticalScrollIndicator={false}
      refreshControl={
        onRefresh ? (
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[COLORS.primary]}
            tintColor={COLORS.primary}
          />
        ) : undefined
      }
      onEndReached={onLoadMore}
      onEndReachedThreshold={0.1}
      ListEmptyComponent={renderEmpty}
      ListHeaderComponent={ListHeaderComponent}
      ListFooterComponent={renderFooter}
      getItemLayout={getItemLayout}
      removeClippedSubviews={true}
      maxToRenderPerBatch={10}
      windowSize={10}
      initialNumToRender={6}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    padding: SPACING.lg,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.xxxl,
  },
  row: {
    justifyContent: 'space-between',
  },
  productCard: {
    marginBottom: SPACING.md,
  },
  leftCard: {
    marginRight: SPACING.sm,
  },
  rightCard: {
    marginLeft: SPACING.sm,
  },
  fullWidthCard: {
    width: '100%',
  },
  centerContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.xxxl,
  },
  loadingText: {
    fontSize: FONTS.md,
    color: COLORS.textSecondary,
    marginTop: SPACING.md,
  },
  emptyTitle: {
    fontSize: FONTS.xl,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginTop: SPACING.lg,
    marginBottom: SPACING.sm,
  },
  emptySubtitle: {
    fontSize: FONTS.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  footerLoader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.lg,
  },
  footerText: {
    fontSize: FONTS.sm,
    color: COLORS.textSecondary,
    marginLeft: SPACING.sm,
  },
});

export default ProductGrid;
