import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useRoute, useNavigation } from '@react-navigation/native';

import { WooCommerceCategory } from '../types';
import { COLORS, FONTS, SPACING, SHADOWS } from '../constants';
import { useApp } from '../context/AppContext';
import wooCommerceAPI from '../services/woocommerce';
import ProductGrid from '../components/product/ProductGrid';
import ProductFiltersComponent, { ProductFilters } from '../components/product/ProductFilters';
import { useCategoryProducts } from '../hooks/useProductSearch';

const CategoryDetailScreen: React.FC = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { setError } = useApp();
  
  const { categoryId, categoryName } = route.params as { 
    categoryId: number; 
    categoryName: string; 
  };

  const [category, setCategory] = useState<WooCommerceCategory | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [categories, setCategories] = useState<WooCommerceCategory[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const {
    products,
    loading,
    error,
    hasSearched,
    search,
    loadMore,
    hasMore,
    refresh,
    refreshing,
  } = useCategoryProducts(categoryId);

  useEffect(() => {
    loadCategoryDetails();
    loadCategories();
  }, [categoryId]);

  useEffect(() => {
    if (error) {
      setError(error);
    }
  }, [error, setError]);

  const loadCategoryDetails = async () => {
    try {
      const categoryData = await wooCommerceAPI.getCategory(categoryId);
      setCategory(categoryData);
      
      // Update navigation title
      navigation.setOptions({
        title: categoryData.name,
      });
    } catch (error) {
      console.error('Error loading category details:', error);
      setError('Failed to load category details.');
    }
  };

  const loadCategories = async () => {
    try {
      const categoriesData = await wooCommerceAPI.getCategories();
      setCategories(categoriesData);
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  const navigateToProduct = (productId: number) => {
    navigation.navigate('ProductDetail', { productId });
  };

  const handleFiltersApply = (filters: ProductFilters) => {
    // Always include the current category in filters
    const categoryFilters = { ...filters, category: categoryId };
    search('', categoryFilters);
  };

  const getActiveFiltersCount = (): number => {
    // Count filters excluding the category filter since it's always active
    let count = 0;
    // We would need to track current filters to count them
    // For now, return 0
    return count;
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.headerLeft}>
        <Text style={styles.categoryTitle}>{categoryName}</Text>
        {category && (
          <Text style={styles.productCount}>
            {category.count} {category.count === 1 ? 'product' : 'products'}
          </Text>
        )}
      </View>
      
      <View style={styles.headerActions}>
        <TouchableOpacity
          style={[styles.actionButton, viewMode === 'grid' && styles.actionButtonActive]}
          onPress={() => setViewMode('grid')}
        >
          <Ionicons 
            name="grid-outline" 
            size={20} 
            color={viewMode === 'grid' ? COLORS.white : COLORS.gray} 
          />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionButton, viewMode === 'list' && styles.actionButtonActive]}
          onPress={() => setViewMode('list')}
        >
          <Ionicons 
            name="list-outline" 
            size={20} 
            color={viewMode === 'list' ? COLORS.white : COLORS.gray} 
          />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.filterButton}
          onPress={() => setShowFilters(true)}
        >
          <Ionicons name="options-outline" size={20} color={COLORS.primary} />
          {getActiveFiltersCount() > 0 && (
            <View style={styles.filterBadge}>
              <Text style={styles.filterBadgeText}>{getActiveFiltersCount()}</Text>
            </View>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderCategoryDescription = () => {
    if (!category?.description) return null;
    
    return (
      <View style={styles.descriptionContainer}>
        <Text style={styles.description}>{category.description}</Text>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {renderHeader()}
      
      <ProductGrid
        products={products}
        loading={loading}
        refreshing={refreshing}
        onRefresh={refresh}
        onLoadMore={loadMore}
        onProductPress={navigateToProduct}
        numColumns={viewMode === 'grid' ? 2 : 1}
        emptyMessage="No products in this category"
        emptyIcon="bag-outline"
        ListHeaderComponent={renderCategoryDescription()}
      />

      <ProductFiltersComponent
        visible={showFilters}
        onClose={() => setShowFilters(false)}
        onApply={handleFiltersApply}
        categories={categories}
        currentFilters={{ category: categoryId }}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    ...SHADOWS.light,
  },
  headerLeft: {
    flex: 1,
  },
  categoryTitle: {
    fontSize: FONTS.xl,
    fontWeight: '600',
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  productCount: {
    fontSize: FONTS.sm,
    color: COLORS.textSecondary,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
  },
  actionButtonActive: {
    backgroundColor: COLORS.primary,
  },
  filterButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  filterBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: COLORS.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },
  filterBadgeText: {
    color: COLORS.white,
    fontSize: 10,
    fontWeight: '600',
  },
  descriptionContainer: {
    backgroundColor: COLORS.white,
    margin: SPACING.lg,
    padding: SPACING.lg,
    borderRadius: SPACING.md,
    ...SHADOWS.light,
  },
  description: {
    fontSize: FONTS.md,
    color: COLORS.textSecondary,
    lineHeight: FONTS.lineHeight.md,
  },
});

export default CategoryDetailScreen;
