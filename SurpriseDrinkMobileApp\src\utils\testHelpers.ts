// Test utilities and mock data for development and testing

import { WooCommerceProduct, WooCommerceCategory, WooCommerceCustomer } from '../types';

// Mock product data
export const mockProducts: WooCommerceProduct[] = [
  {
    id: 1,
    name: "Premium Lace Bra Set",
    slug: "premium-lace-bra-set",
    permalink: "https://example.com/product/premium-lace-bra-set",
    date_created: "2024-01-01T00:00:00",
    date_modified: "2024-01-01T00:00:00",
    type: "simple",
    status: "publish",
    featured: true,
    catalog_visibility: "visible",
    description: "Luxurious lace bra set with matching panties. Made from premium materials for ultimate comfort and style.",
    short_description: "Premium lace bra set with matching panties",
    sku: "LBS001",
    price: "89.99",
    regular_price: "89.99",
    sale_price: "",
    date_on_sale_from: null,
    date_on_sale_to: null,
    on_sale: false,
    purchasable: true,
    total_sales: 45,
    virtual: false,
    downloadable: false,
    downloads: [],
    download_limit: -1,
    download_expiry: -1,
    external_url: "",
    button_text: "",
    tax_status: "taxable",
    tax_class: "",
    manage_stock: true,
    stock_quantity: 25,
    backorders: "no",
    backorders_allowed: false,
    backordered: false,
    low_stock_amount: null,
    sold_individually: false,
    weight: "0.2",
    dimensions: { length: "10", width: "8", height: "2" },
    shipping_required: true,
    shipping_taxable: true,
    shipping_class: "",
    shipping_class_id: 0,
    reviews_allowed: true,
    average_rating: "4.5",
    rating_count: 12,
    upsell_ids: [],
    cross_sell_ids: [],
    parent_id: 0,
    purchase_note: "",
    categories: [
      { id: 1, name: "Lingerie", slug: "lingerie", parent: 0, description: "", display: "default", image: null, menu_order: 0, count: 15 }
    ],
    tags: [],
    images: [
      {
        id: 1,
        date_created: "2024-01-01T00:00:00",
        date_modified: "2024-01-01T00:00:00",
        src: "https://via.placeholder.com/400x400/E91E63/FFFFFF?text=Lace+Bra",
        name: "lace-bra-1",
        alt: "Premium Lace Bra Set"
      }
    ],
    attributes: [],
    default_attributes: [],
    variations: [],
    grouped_products: [],
    menu_order: 0,
    price_html: "<span class=\"price\">$89.99</span>",
    related_ids: [2, 3],
    meta_data: [],
    stock_status: "instock"
  },
  {
    id: 2,
    name: "Silk Nightgown",
    slug: "silk-nightgown",
    permalink: "https://example.com/product/silk-nightgown",
    date_created: "2024-01-02T00:00:00",
    date_modified: "2024-01-02T00:00:00",
    type: "simple",
    status: "publish",
    featured: false,
    catalog_visibility: "visible",
    description: "Elegant silk nightgown perfect for special occasions or everyday luxury.",
    short_description: "Elegant silk nightgown",
    sku: "SNG001",
    price: "129.99",
    regular_price: "149.99",
    sale_price: "129.99",
    date_on_sale_from: null,
    date_on_sale_to: null,
    on_sale: true,
    purchasable: true,
    total_sales: 23,
    virtual: false,
    downloadable: false,
    downloads: [],
    download_limit: -1,
    download_expiry: -1,
    external_url: "",
    button_text: "",
    tax_status: "taxable",
    tax_class: "",
    manage_stock: true,
    stock_quantity: 15,
    backorders: "no",
    backorders_allowed: false,
    backordered: false,
    low_stock_amount: null,
    sold_individually: false,
    weight: "0.3",
    dimensions: { length: "12", width: "10", height: "1" },
    shipping_required: true,
    shipping_taxable: true,
    shipping_class: "",
    shipping_class_id: 0,
    reviews_allowed: true,
    average_rating: "4.8",
    rating_count: 8,
    upsell_ids: [],
    cross_sell_ids: [],
    parent_id: 0,
    purchase_note: "",
    categories: [
      { id: 2, name: "Nightwear", slug: "nightwear", parent: 0, description: "", display: "default", image: null, menu_order: 0, count: 10 }
    ],
    tags: [],
    images: [
      {
        id: 2,
        date_created: "2024-01-02T00:00:00",
        date_modified: "2024-01-02T00:00:00",
        src: "https://via.placeholder.com/400x400/9C27B0/FFFFFF?text=Silk+Nightgown",
        name: "silk-nightgown-1",
        alt: "Silk Nightgown"
      }
    ],
    attributes: [],
    default_attributes: [],
    variations: [],
    grouped_products: [],
    menu_order: 0,
    price_html: "<span class=\"price\"><del>$149.99</del> <ins>$129.99</ins></span>",
    related_ids: [1, 3],
    meta_data: [],
    stock_status: "instock"
  }
];

// Mock categories
export const mockCategories: WooCommerceCategory[] = [
  {
    id: 1,
    name: "Lingerie",
    slug: "lingerie",
    parent: 0,
    description: "Premium lingerie collection",
    display: "default",
    image: {
      id: 10,
      date_created: "2024-01-01T00:00:00",
      date_modified: "2024-01-01T00:00:00",
      src: "https://via.placeholder.com/200x200/E91E63/FFFFFF?text=Lingerie",
      name: "lingerie-category",
      alt: "Lingerie Category"
    },
    menu_order: 1,
    count: 15
  },
  {
    id: 2,
    name: "Nightwear",
    slug: "nightwear",
    parent: 0,
    description: "Comfortable nightwear collection",
    display: "default",
    image: {
      id: 11,
      date_created: "2024-01-01T00:00:00",
      date_modified: "2024-01-01T00:00:00",
      src: "https://via.placeholder.com/200x200/9C27B0/FFFFFF?text=Nightwear",
      name: "nightwear-category",
      alt: "Nightwear Category"
    },
    menu_order: 2,
    count: 10
  }
];

// Test API connectivity
export const testApiConnection = async (): Promise<boolean> => {
  try {
    const response = await fetch('https://surprisedrink.s2-tastewp.com/wp-json/wc/v3/products?per_page=1', {
      headers: {
        'Authorization': 'Basic ' + btoa('ck_3526e1fdad71a83efe18181c26ca32caa5d73263:cs_e6320b015cda94e4a91cd27a32cc25839db4dce7')
      }
    });
    return response.ok;
  } catch (error) {
    console.error('API connection test failed:', error);
    return false;
  }
};

// Development mode helpers
export const isDevelopment = __DEV__;

export const log = (message: string, data?: any) => {
  if (isDevelopment) {
    console.log(`[SurpriseDrink] ${message}`, data || '');
  }
};

export const logError = (message: string, error?: any) => {
  if (isDevelopment) {
    console.error(`[SurpriseDrink Error] ${message}`, error || '');
  }
};

// Performance monitoring
export const measurePerformance = async <T>(
  name: string,
  operation: () => Promise<T>
): Promise<T> => {
  const start = Date.now();
  try {
    const result = await operation();
    const duration = Date.now() - start;
    log(`Performance: ${name} took ${duration}ms`);
    return result;
  } catch (error) {
    const duration = Date.now() - start;
    logError(`Performance: ${name} failed after ${duration}ms`, error);
    throw error;
  }
};

// Mock API responses for offline development
export const mockApiResponses = {
  products: mockProducts,
  categories: mockCategories,
  featuredProducts: mockProducts.filter(p => p.featured),
  onSaleProducts: mockProducts.filter(p => p.on_sale),
  latestProducts: mockProducts.sort((a, b) => 
    new Date(b.date_created).getTime() - new Date(a.date_created).getTime()
  ),
};

// Utility to simulate API delay
export const simulateApiDelay = (ms: number = 1000): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};
